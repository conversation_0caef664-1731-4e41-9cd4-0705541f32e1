"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[9336],{69336:(e,t,o)=>{o.r(t),o.d(t,{ModalEditor:()=>me});var r=o(86087),n=o(47143),c=o(56427),l=o(29491),a=o(92279),s=o(4921),i=o(41233),m=o(94715),d=o(26664),u=o(27723),p=o(42059);function h({onClick:e}){return(0,r.createElement)(c.<PERSON><PERSON>,{className:"woocommerce-iframe-editor__back-button",icon:p.A,onClick:e},(0,u.__)("Back","woocommerce"))}function _({children:e,enableResizing:t,settings:o,...n}){const c=(0,m.__unstableUseMouseMoveTypingReset)();return(0,r.createElement)(m.__unstableIframe,{ref:c,name:"editor-canvas",className:"edit-site-visual-editor__editor-canvas",...n},(0,r.createElement)(r.Fragment,null,(0,r.createElement)(m.__unstableEditorStyles,{styles:o?.styles}),(0,r.createElement)("style",null,".is-root-container {\n\t\t\t\t\t\t\t\tpadding: 36px;\n\t\t\t\t\t\t\t\tdisplay: flow-root;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbody { position: relative; }"),t&&(0,r.createElement)("style",null,".is-root-container { min-height: 0 !important; }"),e))}const b=(0,r.createContext)({hasRedo:!1,hasUndo:!1,isDocumentOverviewOpened:!1,isInserterOpened:!1,redo:()=>{},setIsDocumentOverviewOpened:()=>{},setIsInserterOpened:()=>{},undo:()=>{}});var w=o(67606),v=o(2111),f=o(52887),E=o(88150),g=o(22794),k=o(51388),C=o(48558);const S=(0,r.forwardRef)((function(e,t){const o=(0,C.isAppleOS)()?C.displayShortcut.primaryShift("z"):C.displayShortcut.primary("y"),{hasRedo:n,redo:l}=(0,r.useContext)(b);return(0,r.createElement)(c.Button,{...e,ref:t,icon:(0,u.isRTL)()?k.A:g.A,label:(0,u.__)("Redo","woocommerce"),shortcut:o,"aria-disabled":!n,onClick:n?l:void 0,className:"editor-history__redo"})})),y=(0,r.forwardRef)((function(e,t){const{hasUndo:o,undo:n}=(0,r.useContext)(b);return(0,r.createElement)(c.Button,{...e,ref:t,icon:(0,u.isRTL)()?g.A:k.A,label:(0,u.__)("Undo","woocommerce"),shortcut:C.displayShortcut.primary("z"),"aria-disabled":!o,onClick:o?n:void 0,className:"editor-history__undo"})}));var I=o(6006);const O=(0,r.forwardRef)((function(e,t){const{isDocumentOverviewOpened:o,setIsDocumentOverviewOpened:n}=(0,r.useContext)(b);return(0,r.createElement)(c.Button,{...e,ref:t,icon:I.A,isPressed:o,label:(0,u.__)("Document overview","woocommerce"),shortcut:C.displayShortcut.access("o"),onClick:function(){n(!o)},className:"document-overview"})}));var T=o(78269),R=o(74997),B=o(83306);const D=()=>{const{createNotice:e}=(0,n.useDispatch)("core/notices"),{blocks:t}=(0,n.useSelect)((e=>{const{getBlocks:t}=e(m.store);return{blocks:t()}}),[]),o=(0,l.useCopyToClipboard)((()=>(0,R.serialize)(t)),(()=>{e("success",(0,u.__)("All content copied.","woocommerce"))}));return(0,r.createElement)(c.MenuItem,{ref:o,role:"menuitem",onClick:()=>{(0,B.recordEvent)("product_iframe_editor_copy_all_content_menu_item_click")},disabled:!t.length},(0,u.__)("Copy all content","woocommerce"))};var x=o(67237);const z=()=>(0,r.createElement)(c.MenuItem,{role:"menuitem",icon:x.A,href:(0,u.__)("https://wordpress.org/documentation/article/wordpress-block-editor/","woocommerce"),onClick:()=>{(0,B.recordEvent)("product_iframe_editor_help_menu_item_click")},target:"_blank",rel:"noopener noreferrer"},(0,u.__)("Help","woocommerce"),(0,r.createElement)(c.VisuallyHidden,{as:"span"},(0,u.__)("(opens in a new tab)","woocommerce"))),N=()=>(0,r.createElement)(c.MenuGroup,{label:(0,u.__)("Tools","woocommerce")},(0,r.createElement)(D,null),(0,r.createElement)(z,null));function A(){const{set:e}=(0,n.useDispatch)(i.store);return(0,l.useViewportMatch)("medium")?(0,r.createElement)(c.MenuGroup,{label:(0,u.__)("View","woocommerce")},(0,r.createElement)(i.PreferenceToggleMenuItem,{scope:"core",name:"fixedToolbar",onToggle:()=>{e("core","distractionFree",!1)},label:(0,u.__)("Top toolbar","woocommerce"),info:(0,u.__)("Access all block and document tools in a single place","woocommerce"),messageActivated:(0,u.__)("Top toolbar activated","woocommerce"),messageDeactivated:(0,u.__)("Top toolbar deactivated","woocommerce")})):null}var M=o(36952),L=o(3846);const P=()=>(0,r.createElement)(L.Y,null,(e=>(0,r.createElement)(r.Fragment,null,(0,r.createElement)(A,null),(0,r.createElement)(T.A.Slot,{name:M.g1,label:(0,u.__)("Plugins","woocommerce"),as:c.MenuGroup,fillProps:{onClick:e}}),(0,r.createElement)(N,null))));function H({onSave:e=()=>{},onCancel:t=()=>{}}){const{isInserterOpened:o,setIsInserterOpened:a}=(0,r.useContext)(b),[d,p]=(0,r.useState)(!0),h=(0,l.useViewportMatch)("medium"),_=(0,r.useRef)(null),{isInserterEnabled:g,isTextModeEnabled:k,hasBlockSelection:C,hasFixedToolbar:I}=(0,n.useSelect)((e=>{var t,o;const{hasInserterItems:r,getBlockRootClientId:n,getBlockSelectionEnd:c,__unstableGetEditorMode:l,getBlockSelectionStart:a}=e(m.store),{get:s}=e(i.store);return{isTextModeEnabled:"text"===l(),isInserterEnabled:r(null!==(t=n(null!==(o=c())&&void 0!==o?o:""))&&void 0!==t?t:void 0),hasBlockSelection:!!a(),hasFixedToolbar:s("core","fixedToolbar")}}),[]),T=(0,r.useCallback)((()=>a(!o)),[o,a]);return(0,r.useEffect)((()=>{C&&p(!1)}),[C]),(0,r.createElement)("div",{className:"woocommerce-iframe-editor__header"},(0,r.createElement)("div",{className:"woocommerce-iframe-editor__header-left"},(0,r.createElement)(m.NavigableToolbar,{className:"woocommerce-iframe-editor-document-tools","aria-label":(0,u.__)("Document tools","woocommerce"),variant:"unstyled"},(0,r.createElement)("div",{className:"woocommerce-iframe-editor-document-tools__left"},(0,r.createElement)(c.ToolbarItem,{ref:_,as:c.Button,className:"woocommerce-iframe-editor__header-inserter-toggle",variant:"primary",isPressed:o,onMouseDown:e=>{e.preventDefault()},onClick:T,disabled:!g,icon:w.A,label:(0,u.__)("Toggle block inserter","woocommerce"),"aria-expanded":o,showTooltip:!0}),h&&(0,r.createElement)(c.ToolbarItem,{as:m.ToolSelector,disabled:k,size:"compact"}),(0,r.createElement)(c.ToolbarItem,{as:y,size:"compact"}),(0,r.createElement)(c.ToolbarItem,{as:S,size:"compact"}),(0,r.createElement)(c.ToolbarItem,{as:O,size:"compact"}))),I&&h&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:(0,s.A)("selected-block-tools-wrapper",{"is-collapsed":d})},(0,r.createElement)(m.BlockToolbar,{hideDragHandle:!0})),(0,r.createElement)(c.Popover.Slot,{name:"block-toolbar"}),C&&(0,r.createElement)(c.Button,{className:"edit-post-header__block-tools-toggle",icon:d?v.A:f.A,onClick:()=>{p((e=>!e))},label:d?(0,u.__)("Show block tools","woocommerce"):(0,u.__)("Hide block tools","woocommerce")}))),(0,r.createElement)("div",{className:"woocommerce-iframe-editor__header-right"},(0,r.createElement)(c.Button,{variant:"tertiary",className:"woocommerce-modal-actions__cancel-button",onClick:t,text:(0,u.__)("Cancel","woocommerce")}),(0,r.createElement)(c.Button,{variant:"primary",className:"woocommerce-modal-actions__done-button",onClick:e,text:(0,u.__)("Done","woocommerce")}),(0,r.createElement)(E.A.Slot,{scope:M.L1}),(0,r.createElement)(P,null)))}const V=()=>{const e=(0,n.useRegistry)();return(0,r.useEffect)((()=>{e.register(d.store)}),[e]),null},F=20;function U({direction:e,resizeWidthBy:t}){return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("button",{className:`resizable-editor__drag-handle is-${e}`,"aria-label":(0,u.__)("Drag to resize","woocommerce"),"aria-describedby":`resizable-editor__resize-help-${e}`,onKeyDown:function(o){const{keyCode:r}=o;"left"===e&&r===C.LEFT||"right"===e&&r===C.RIGHT?t(F):("left"===e&&r===C.RIGHT||"right"===e&&r===C.LEFT)&&t(-F)}}),(0,r.createElement)(c.VisuallyHidden,{id:`resizable-editor__resize-help-${e}`},(0,u.__)("Use left and right arrow keys to resize the canvas.","woocommerce")))}const K={position:void 0,userSelect:void 0,cursor:void 0,width:void 0,height:void 0,top:void 0,right:void 0,bottom:void 0,left:void 0};function W({enableResizing:e,height:t,children:o}){const[n,l]=(0,r.useState)("100%"),a=(0,r.useRef)(),s=(0,r.useCallback)((e=>{a.current&&l((a.current.offsetWidth+e).toString())}),[]);return(0,r.createElement)(c.ResizableBox,{ref:e=>{a.current=e?.resizable},size:{width:e?n:"100%",height:e&&t?t:"100%"},onResizeStop:(e,t,o)=>{l(o.style.width)},minWidth:300,maxWidth:"100%",maxHeight:"100%",minHeight:t,enable:{right:e,left:e},showHandle:e,resizeRatio:2,handleComponent:{left:(0,r.createElement)(U,{direction:"left",resizeWidthBy:s}),right:(0,r.createElement)(U,{direction:"right",resizeWidthBy:s})},handleClasses:void 0,handleStyles:{left:K,right:K}},o)}function G(){const{setIsInserterOpened:e}=(0,r.useContext)(b),t=(0,l.useViewportMatch)("medium","<"),{rootClientId:o}=(0,n.useSelect)((e=>{const{getBlockRootClientId:t}=e(m.store);return{rootClientId:t("")}}),[]),c=(0,r.useCallback)((()=>e(!1)),[e]),a=(0,r.useCallback)((e=>{e.keyCode!==C.ESCAPE||e.defaultPrevented||(e.preventDefault(),c())}),[c]),s=(0,r.useRef)(null);return(0,r.useEffect)((()=>{s.current?.focusSearch?.()}),[]),(0,r.createElement)("div",{onKeyDown:e=>a(e),className:"woocommerce-iframe-editor__inserter-panel"},(0,r.createElement)("div",{className:"woocommerce-iframe-editor__inserter-panel-content"},(0,r.createElement)(m.__experimentalLibrary,{showInserterHelpPanel:!0,shouldFocusBlock:t,rootClientId:o,ref:s,onClose:c,onSelect:()=>{t&&c()}})))}var $=o(91218);function J(){const{setIsDocumentOverviewOpened:e}=(0,r.useContext)(b),t=(0,l.useFocusOnMount)("firstElement"),o=(0,l.useFocusReturn)(),n=(0,l.useFocusReturn)(),[a,s]=(0,r.useState)(null),[i,d]=(0,r.useState)("list-view"),p=(0,r.useRef)(null),h=(0,l.useMergeRefs)([n,t,p,s]);return(0,r.createElement)("div",{className:"woocommerce-iframe-editor__document-overview-sidebar",onKeyDown:function(t){"Escape"!==t.code||t.defaultPrevented||(t.preventDefault(),e(!1))}},(0,r.createElement)(c.Button,{className:"woocommerce-iframe-editor__document-overview-sidebar-close-button",ref:o,icon:$.A,label:(0,u.__)("Close","woocommerce"),onClick:()=>e(!1)}),(0,r.createElement)(c.TabPanel,{className:"woocommerce-iframe-editor__document-overview-sidebar-tab-panel",initialTabName:i,onSelect:d,tabs:[{name:"list-view",title:(0,u.__)("List View","woocommerce"),className:"woocommerce-iframe-editor__document-overview-sidebar-tab-item"}]},(e=>(0,r.createElement)("div",{className:"woocommerce-iframe-editor__document-overview-sidebar-tab-content",ref:h},"list-view"===e.name?(0,r.createElement)(m.__experimentalListView,{dropZoneElement:a}):null))))}function j(){const{isInserterOpened:e,isDocumentOverviewOpened:t}=(0,r.useContext)(b);return e?(0,r.createElement)(G,null):t?(0,r.createElement)(J,null):null}const q=(0,r.createElement)("svg",{width:"24",height:"24",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24","aria-hidden":"true",focusable:"false"},(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18 4H6c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zM8.5 18.5H6c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h2.5v13zm10-.5c0 .3-.2.5-.5.5h-8v-13h8c.3 0 .5.2.5.5v12z"})),Y=(0,r.createElement)("svg",{width:"24",height:"24",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24","aria-hidden":"true",focusable:"false"},(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18 4H6c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-4 14.5H6c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h8v13zm4.5-.5c0 .3-.2.5-.5.5h-2.5v-13H18c.3 0 .5.2.5.5v12z"}));var Z=o(96699);const Q=()=>(0,r.createElement)("strong",null,(0,u.__)("Settings","woocommerce")),X=({smallScreenTitle:e})=>(0,r.createElement)(Z.w,{identifier:M.PK,title:(0,u.__)("Settings","woocommerce"),icon:(0,u.isRTL)()?Y:q,isActiveByDefault:!0,header:(0,r.createElement)(Q,null),closeLabel:(0,u.__)("Close settings","woocommerce"),smallScreenTitle:e},(0,r.createElement)(m.BlockInspector,null)),ee=50;var te=o(3450),oe=o(53031);const re=()=>{const{isDocumentOverviewOpened:e,redo:t,setIsDocumentOverviewOpened:o,undo:c}=(0,r.useContext)(b),{isSettingsSidebarOpen:l}=(0,n.useSelect)((e=>{const{getActiveComplementaryArea:t}=e(d.store);return{isSettingsSidebarOpen:t(M.L1)===M.PK}}),[]),{disableComplementaryArea:a,enableComplementaryArea:s}=(0,n.useDispatch)(d.store);return(0,oe.useShortcut)("woocommerce/product-editor/modal-block-editor/undo",(e=>{c(),e.preventDefault()})),(0,oe.useShortcut)("woocommerce/product-editor/modal-block-editor/redo",(e=>{t(),e.preventDefault()})),(0,oe.useShortcut)("woocommerce/product-editor/modal-block-editor/toggle-list-view",(t=>{o(!e),t.preventDefault()})),(0,oe.useShortcut)("woocommerce/product-editor/modal-block-editor/toggle-sidebar",(e=>{l?a(M.L1):s(M.L1,M.PK),e.preventDefault()})),null},ne=()=>{const{registerShortcut:e}=(0,n.useDispatch)(oe.store);return(0,r.useEffect)((()=>{e({name:"woocommerce/product-editor/modal-block-editor/undo",category:"global",description:(0,u.__)("Undo your last changes.","woocommerce"),keyCombination:{modifier:"primary",character:"z"}}),e({name:"woocommerce/product-editor/modal-block-editor/redo",category:"global",description:(0,u.__)("Redo your last undo.","woocommerce"),keyCombination:{modifier:"primaryShift",character:"z"},aliases:(0,C.isAppleOS)()?[]:[{modifier:"primary",character:"y"}]}),e({name:"woocommerce/product-editor/modal-block-editor/toggle-list-view",category:"global",description:(0,u.__)("Open the block list view.","woocommerce"),keyCombination:{modifier:"access",character:"o"}}),e({name:"woocommerce/product-editor/modal-block-editor/toggle-sidebar",category:"global",description:(0,u.__)("Show or hide the Settings sidebar.","woocommerce"),keyCombination:{modifier:"primaryShift",character:","}})}),[e]),null},ce="SET_IS_INSERTER_OPENED",le="SET_IS_LISTVIEW_OPENED",ae={isInserterOpened:!1,isListViewOpened:!1};function se(e,t){switch(t.type){case ce:return{...e,isInserterOpened:t.value,isListViewOpened:!t.value&&e.isListViewOpened};case le:return{...e,isListViewOpened:t.value,isInserterOpened:!t.value&&e.isInserterOpened}}return e}function ie({onChange:e=()=>{},onClose:t,onInput:o=()=>{},settings:u,showBackButton:p=!1,name:w}){const[v]=(0,l.useResizeObserver)(),[f,E]=(0,r.useState)([]),g=(0,n.useSelect)((e=>e(te.p).getModalEditorBlocks()),[]),{setModalEditorBlocks:k,setModalEditorContentHasChanged:C}=(0,n.useDispatch)(te.p),{appendEdit:S,hasRedo:y,hasUndo:I,redo:O,undo:T}=function({maxHistory:e=ee,setBlocks:t}){const[o,n]=(0,r.useState)([]),[c,a]=(0,r.useState)(0),s=(0,l.useDebounce)((0,r.useCallback)((t=>{const r=[...o.slice(0,c+1),t].slice(-1*e);n(r),a(r.length-1)}),[o,e,c]),500),i=(0,r.useCallback)((()=>{s.flush();const e=Math.max(0,c-1);o[e]&&(t(o[e]),a(e))}),[s,o,c,t]),m=(0,r.useCallback)((()=>{s.flush();const e=Math.min(o.length-1,c+1);o[e]&&(t(o[e]),a(e))}),[s,o,c,t]);return{appendEdit:s,hasRedo:!!o.length&&c<o.length-1,hasUndo:!!o.length&&c>0,redo:m,undo:i}}({setBlocks:E});(0,r.useEffect)((()=>{S(g),E(g)}),[]);const[{isInserterOpened:R,isListViewOpened:B},D]=(0,r.useReducer)(se,ae),x=(0,r.useCallback)((e=>{D({type:ce,value:e})}),[]),z=(0,r.useCallback)((e=>{D({type:le,value:e})}),[]),{clearSelectedBlock:N,updateSettings:A}=(0,n.useDispatch)(m.store),L=(0,n.useSelect)((e=>e(m.store).getSettings()),[]),{hasFixedToolbar:P}=(0,n.useSelect)((e=>{const{get:t}=e(i.store);return{hasFixedToolbar:t("core","fixedToolbar")}}),[]);(0,r.useEffect)((()=>{A(productBlockEditorSettings)}),[]);const F=u||L;return(0,r.createElement)("div",{className:"woocommerce-iframe-editor"},(0,r.createElement)(b.Provider,{value:{hasRedo:y,hasUndo:I,isInserterOpened:R,isDocumentOverviewOpened:B,redo:O,setIsInserterOpened:x,setIsDocumentOverviewOpened:z,undo:T}},(0,r.createElement)(m.BlockEditorProvider,{settings:{...F,hasFixedToolbar:P,templateLock:!1},value:f,onChange:t=>{S(t),E(t),e(t)},onInput:e=>{S(e),E(e),o(e)},useSubRegistry:!0},(0,r.createElement)(V,null),(0,r.createElement)(re,null),(0,r.createElement)(ne,null),(0,r.createElement)(H,{onSave:()=>{k(function(e){if(!e?.length)return!0;if(1===e.length){const t=e[0];if("core/paragraph"===t.name){const{content:e,dropCap:o,backgroundColor:r,...n}=t.attributes,c=!e||!e.trim(),l=!!r,a=Object.keys(n).length>0;if(c&&!l&&!a)return!0}}return!1}(f)?[]:f),C(!0),e(f),t?.()},onCancel:()=>{k(g),e(g),E(g),t?.()}}),(0,r.createElement)("div",{className:"woocommerce-iframe-editor__main"},(0,r.createElement)(j,null),(0,r.createElement)(m.BlockTools,{className:(0,s.A)("woocommerce-iframe-editor__content"),onClick:e=>{e.target===e.currentTarget&&N()}},(0,r.createElement)(m.BlockEditorKeyboardShortcuts.Register,null),p&&t&&(0,r.createElement)(h,{onClick:()=>{setTimeout(t,550)}}),(0,r.createElement)(W,{enableResizing:!0,height:"100%"},(0,r.createElement)(_,{enableResizing:!0,settings:F},v,(0,r.createElement)(m.BlockList,{className:"edit-site-block-editor__block-list wp-site-blocks"})),(0,r.createElement)(c.Popover.Slot,null)),(0,r.createElement)("div",{className:"woocommerce-iframe-editor__content-inserter-clipper"})),(0,r.createElement)(d.ComplementaryArea.Slot,{scope:M.L1})),(0,r.createElement)(a.PluginArea,{scope:"woocommerce-product-editor-modal-block-editor"}),(0,r.createElement)(X,{smallScreenTitle:w}))))}function me({initialBlocks:e,onChange:t,onClose:o,title:a,name:s}){const{closeModalEditor:i}=(0,n.useDispatch)(te.p),m=(0,l.useDebounce)((e=>{t?.(e)}),250);function d(){const e=m.flush();e&&t?.(e),i(),o?.()}return(0,r.createElement)(c.Modal,{className:"woocommerce-modal-editor",title:a,onRequestClose:d,shouldCloseOnClickOutside:!1},(0,r.createElement)(ie,{initialBlocks:e,onInput:m,onChange:m,onClose:d,name:s}))}}}]);