/*! For license information please see index.js.LICENSE.txt */
(()=>{var e,t,r,o,n={36849:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var o=r(51609),n=r(51428);function a(e,t){let r,n,s=[];for(let o=0;o<e.length;o++){const a=e[o];if("string"!==a.type){if(void 0===t[a.value])throw new Error(`Invalid interpolation, missing component node: \`${a.value}\``);if("object"!=typeof t[a.value])throw new Error(`Invalid interpolation, component node must be a ReactElement or null: \`${a.value}\``);if("componentClose"===a.type)throw new Error(`Missing opening component token: \`${a.value}\``);if("componentOpen"===a.type){r=t[a.value],n=o;break}s.push(t[a.value])}else s.push(a.value)}if(r){const i=function(e,t){const r=t[e];let o=0;for(let n=e+1;n<t.length;n++){const e=t[n];if(e.value===r.value){if("componentOpen"===e.type){o++;continue}if("componentClose"===e.type){if(0===o)return n;o--}}}throw new Error("Missing closing component token `"+r.value+"`")}(n,e),c=a(e.slice(n+1,i),t),l=(0,o.cloneElement)(r,{},c);if(s.push(l),i<e.length-1){const r=a(e.slice(i+1),t);s=s.concat(r)}}return s=s.filter(Boolean),0===s.length?null:1===s.length?s[0]:(0,o.createElement)(o.Fragment,null,...s)}function s(e){const{mixedString:t,components:r,throwErrors:o}=e;if(!r)return t;if("object"!=typeof r){if(o)throw new Error(`Interpolation Error: unable to process \`${t}\` because components is not an object`);return t}const s=(0,n.A)(t);try{return a(s,r)}catch(e){if(o)throw new Error(`Interpolation Error: unable to process \`${t}\` because of error \`${e.message}\``);return t}}},51428:(e,t,r)=>{"use strict";function o(e){return e.startsWith("{{/")?{type:"componentClose",value:e.replace(/\W/g,"")}:e.endsWith("/}}")?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.startsWith("{{")?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}function n(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(o)}r.d(t,{A:()=>n})},24148:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var o=r(86087);const n=(0,o.forwardRef)((function({icon:e,size:t=24,...r},n){return(0,o.cloneElement)(e,{width:t,height:t,...r,ref:n})}))},7833:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(5573),n=r(39793);const a=(0,n.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(o.Path,{fillRule:"evenodd",d:"M5 5.5h14a.5.5 0 01.5.5v1.5a.5.5 0 01-.5.5H5a.5.5 0 01-.5-.5V6a.5.5 0 01.5-.5zM4 9.232A2 2 0 013 7.5V6a2 2 0 012-2h14a2 2 0 012 2v1.5a2 2 0 01-1 1.732V18a2 2 0 01-2 2H6a2 2 0 01-2-2V9.232zm1.5.268V18a.5.5 0 00.5.5h12a.5.5 0 00.5-.5V9.5h-13z",clipRule:"evenodd"})})},72744:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(5573),n=r(39793);const a=(0,n.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(o.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})})},47804:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(5573),n=r(39793);const a=(0,n.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(o.Path,{d:"M13 11.8l6.1-6.3-1-1-6.1 6.2-6.1-6.2-1 1 6.1 6.3-6.5 6.7 1 1 6.5-6.6 6.5 6.6 1-1z"})})},95687:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(5573),n=r(39793);const a=(0,n.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(o.Path,{fillRule:"evenodd",d:"M7.25 16.437a6.5 6.5 0 1 1 9.5 0V16A2.75 2.75 0 0 0 14 13.25h-4A2.75 2.75 0 0 0 7.25 16v.437Zm1.5 1.193a6.47 6.47 0 0 0 3.25.87 6.47 6.47 0 0 0 3.25-.87V16c0-.69-.56-1.25-1.25-1.25h-4c-.69 0-1.25.56-1.25 1.25v1.63ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm10-2a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z",clipRule:"evenodd"})})},6513:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(5573),n=r(39793);const a=(0,n.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(o.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})})},45521:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(5573),n=r(39793);const a=(0,n.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(o.Path,{d:"M12 4.75a7.25 7.25 0 100 14.5 7.25 7.25 0 000-14.5zM3.25 12a8.75 8.75 0 1117.5 0 8.75 8.75 0 01-17.5 0zM12 8.75a1.5 1.5 0 01.167 2.99c-.465.052-.917.44-.917 1.01V14h1.5v-.845A3 3 0 109 10.25h1.5a1.5 1.5 0 011.5-1.5zM11.25 15v1.5h1.5V15h-1.5z"})})},16254:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(5573),n=r(39793);const a=(0,n.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(o.Path,{d:"M17.031 4.703 15.576 4l-1.56 3H14v.03l-2.324 4.47H9.5V13h1.396l-1.502 2.889h-.95a3.694 3.694 0 0 1 0-7.389H10V7H8.444a5.194 5.194 0 1 0 0 10.389h.17L7.5 19.53l1.416.719L15.049 8.5h.507a3.694 3.694 0 0 1 0 7.39H14v1.5h1.556a5.194 5.194 0 0 0 .273-10.383l1.202-2.304Z"})})},99669:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(5573),n=r(39793);const a=(0,n.jsxs)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,n.jsx)(o.Path,{d:"M15.5 7.5h-7V9h7V7.5Zm-7 3.5h7v1.5h-7V11Zm7 3.5h-7V16h7v-1.5Z"}),(0,n.jsx)(o.Path,{d:"M17 4H7a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2ZM7 5.5h10a.5.5 0 0 1 .5.5v12a.5.5 0 0 1-.5.5H7a.5.5 0 0 1-.5-.5V6a.5.5 0 0 1 .5-.5Z"})]})},28180:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(5573),n=r(39793);const a=(0,n.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(o.Path,{d:"M10.5 4v4h3V4H15v4h1.5a1 1 0 011 1v4l-3 4v2a1 1 0 01-1 1h-3a1 1 0 01-1-1v-2l-3-4V9a1 1 0 011-1H9V4h1.5zm.5 12.5v2h2v-2l3-4v-3H8v3l3 4z"})})},63861:(e,t,r)=>{"use strict";r.d(t,{q:()=>m,P:()=>g});var o=r(56427),n=r(47143),a=r(27723),s=r(40314),i=r(83306),c=r(39793);const l=()=>(0,c.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)("g",{id:"block-template-part-sidebar",children:(0,c.jsx)("path",{id:"Shape",fillRule:"evenodd",clipRule:"evenodd",d:"M6 4H18C19.1046 4 20 4.89543 20 6V18C20 19.1046 19.1046 20 18 20H6C4.89543 20 4 19.1046 4 18V6C4 4.89543 4.89543 4 6 4ZM18 5.5H6C5.72386 5.5 5.5 5.72386 5.5 6V9H18.5V6C18.5 5.72386 18.2761 5.5 18 5.5ZM18.5 10.5H10L10 18.5H18C18.2761 18.5 18.5 18.2761 18.5 18V10.5Z",fill:"#1E1E1E"})})}),u=()=>(0,c.jsx)("svg",{className:"woocommerce-layout__activity-panel-tab-icon",width:"12",height:"14",viewBox:"0 0 12 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)("rect",{x:"0.5",y:"0.5",width:"11",height:"13",strokeWidth:"1"})}),d=()=>(0,c.jsxs)("svg",{className:"woocommerce-layout__activity-panel-tab-icon",width:"18",height:"14",viewBox:"0 0 18 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,c.jsx)("rect",{x:"0.5",y:"0.5",width:"7",height:"13",strokeWidth:"1"}),(0,c.jsx)("rect",{x:"9.5",y:"0.5",width:"7",height:"13",strokeWidth:"1"})]});var p=r(99915);const{Fill:m,Slot:h}=(0,o.createSlotFill)("DisplayOptions");m.Slot=h;const f=[{value:"single_column",label:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(u,{}),(0,a.__)("Single column","woocommerce")]})},{value:"two_columns",label:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(d,{}),(0,a.__)("Two columns","woocommerce")]})}],g=()=>{const{defaultHomescreenLayout:e}=(0,n.useSelect)((e=>{const{getOption:t}=e(s.optionsStore);return{defaultHomescreenLayout:t("woocommerce_default_homepage_layout")||"single_column"}})),{updateUserPreferences:t,homepage_layout:r}=(0,s.useUserPreferences)(),u=!(0,p.EM)("setup")||window.wcAdminFeatures.analytics;return(0,c.jsx)(h,{children:n=>0!==n.length||u?(0,c.jsx)(o.DropdownMenu,{icon:(0,c.jsx)(l,{}),label:(0,a.__)("Display options","woocommerce"),toggleProps:{className:"woocommerce-layout__activity-panel-tab display-options",onClick:()=>(0,i.recordEvent)("homescreen_display_click")},popoverProps:{className:"woocommerce-layout__activity-panel-popover"},children:({onClose:s})=>(0,c.jsxs)(c.Fragment,{children:[n,u?(0,c.jsx)(o.MenuGroup,{className:"woocommerce-layout__homescreen-display-options",label:(0,a.__)("Layout","woocommerce"),children:(0,c.jsx)(o.MenuItemsChoice,{choices:f,onSelect:e=>{t({homepage_layout:e}),s(),(0,i.recordEvent)("homescreen_display_option",{display_option:e})},value:r||e})}):null]})}):null})}},57882:(e,t,r)=>{"use strict";r.d(t,{JT:()=>v,sY:()=>b});var o=r(27723),n=r(14908),a=r(83306),s=r(98846),i=r(47143),c=r(99669),l=r(5573),u=r(39793);const d=(0,u.jsx)(l.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,u.jsx)(l.Path,{d:"M18 4H6c-1.1 0-2 .9-2 2v12.9c0 .6.5 1.1 1.1 1.1.3 0 .5-.1.8-.3L8.5 17H18c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm.5 11c0 .3-.2.5-.5.5H7.9l-2.4 2.4V6c0-.3.2-.5.5-.5h12c.3 0 .5.2.5.5v9z"})});var p=r(7833),m=r(56427),h=r(96476),f=r(29332),g=r(42288);const y=()=>(0,u.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:[(0,u.jsx)("path",{d:"M0 0h24v24H0z",fill:"none"}),(0,u.jsx)("path",{d:"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.63-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.64 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2zm-2 1H8v-6c0-2.48 1.51-4.5 4-4.5s4 2.02 4 4.5v6z"})]});var w=r(99915);const v="AbbreviatedNotification",b=({thingsToDoNextCount:e})=>{const{ordersToProcessCount:t,reviewsToModerateCount:r,stockNoticesCount:l,isSetupTaskListHidden:b,isExtendedTaskListHidden:_}=(0,i.useSelect)((e=>{const t=(0,f.VJ)(e);return{ordersToProcessCount:(0,f.xC)(e,t),reviewsToModerateCount:(0,g.my)(e),stockNoticesCount:(0,f.G9)(e),isSetupTaskListHidden:!(0,w.Oh)("setup"),isExtendedTaskListHidden:!(0,w.Oh)("extended")}})),x=e=>{(0,a.recordEvent)("activity_panel_click",{task:e})},{Slot:j}=(0,m.createSlotFill)(v),M=(0,h.isWCAdmin)();return(0,u.jsxs)("div",{className:"woocommerce-abbreviated-notifications",children:[e>0&&!_&&(0,u.jsxs)(s.AbbreviatedCard,{className:"woocommerce-abbreviated-notification",icon:(0,u.jsx)(y,{}),href:"admin.php?page=wc-admin#extended_task_list",onClick:()=>x("thingsToDoNext"),type:M?"wc-admin":"wp-admin",children:[(0,u.jsx)(n.Text,{as:"h3",children:(0,o.__)("Things to do next","woocommerce")}),(0,u.jsx)(n.Text,{as:"p",children:(0,o.sprintf)((0,o._n)("You have %d new thing to do","You have %d new things to do",e,"woocommerce"),e)})]}),t>0&&b&&(0,u.jsxs)(s.AbbreviatedCard,{className:"woocommerce-abbreviated-notification",icon:c.A,href:"admin.php?page=wc-admin&opened_panel=orders-panel",onClick:()=>x("ordersToProcess"),type:M?"wc-admin":"wp-admin",children:[(0,u.jsx)(n.Text,{as:"h3",children:(0,o.__)("Orders to fulfill","woocommerce")}),(0,u.jsx)(n.Text,{children:(0,o.sprintf)((0,o._n)("You have %d order to fulfill","You have %d orders to fulfill",t,"woocommerce"),t)})]}),r>0&&b&&(0,u.jsxs)(s.AbbreviatedCard,{className:"woocommerce-abbreviated-notification",icon:d,href:"admin.php?page=wc-admin&opened_panel=reviews-panel",onClick:()=>x("reviewsToModerate"),type:M?"wc-admin":"wp-admin",children:[(0,u.jsx)(n.Text,{as:"h3",children:(0,o.__)("Reviews to moderate","woocommerce")}),(0,u.jsx)(n.Text,{children:(0,o.sprintf)((0,o._n)("You have %d review to moderate","You have %d reviews to moderate",r,"woocommerce"),r)})]}),l>0&&b&&(0,u.jsxs)(s.AbbreviatedCard,{className:"woocommerce-abbreviated-notification",icon:p.A,href:"admin.php?page=wc-admin&opened_panel=stock-panel",onClick:()=>x("stockNotices"),type:M?"wc-admin":"wp-admin",children:[(0,u.jsx)(n.Text,{as:"h3",children:(0,o.__)("Inventory to review","woocommerce")}),(0,u.jsx)(n.Text,{children:(0,o.__)("You have inventory to review and update","woocommerce")})]}),!_&&(0,u.jsx)(j,{})]})}},54574:(e,t,r)=>{"use strict";r.d(t,{A:()=>w});var o=r(27723),n=r(52619),a=r(86087),s=r(56109);const i=(0,a.lazy)((()=>Promise.all([r.e(2791),r.e(3151)]).then(r.bind(r,95220)))),c=(0,a.lazy)((()=>Promise.all([r.e(2791),r.e(3837),r.e(5845)]).then(r.bind(r,60781)))),l=(0,a.lazy)((()=>Promise.all([r.e(2791),r.e(3837),r.e(6071)]).then(r.bind(r,96703)))),u=(0,a.lazy)((()=>Promise.all([r.e(2791),r.e(2304),r.e(7202)]).then(r.bind(r,30457)))),d=(0,a.lazy)((()=>Promise.all([r.e(2791),r.e(3837),r.e(4409)]).then(r.bind(r,3541)))),p=(0,a.lazy)((()=>Promise.all([r.e(2791),r.e(8286)]).then(r.bind(r,35301)))),m=(0,a.lazy)((()=>Promise.all([r.e(2791),r.e(8068)]).then(r.bind(r,15415)))),h=(0,a.lazy)((()=>Promise.all([r.e(2791),r.e(6424)]).then(r.bind(r,47939)))),f=(0,a.lazy)((()=>Promise.all([r.e(2791),r.e(5113)]).then(r.bind(r,4584)))),g=(0,a.lazy)((()=>Promise.all([r.e(2791),r.e(3256)]).then(r.bind(r,2592)))),y=(0,s.Qk)("manageStock","no"),w=()=>{const e=[{report:"revenue",title:(0,o.__)("Revenue","woocommerce"),component:i,navArgs:{id:"woocommerce-analytics-revenue"}},{report:"products",title:(0,o.__)("Products","woocommerce"),component:c,navArgs:{id:"woocommerce-analytics-products"}},{report:"variations",title:(0,o.__)("Variations","woocommerce"),component:l,navArgs:{id:"woocommerce-analytics-variations"}},{report:"orders",title:(0,o.__)("Orders","woocommerce"),component:u,navArgs:{id:"woocommerce-analytics-orders"}},{report:"categories",title:(0,o.__)("Categories","woocommerce"),component:d,navArgs:{id:"woocommerce-analytics-categories"}},{report:"coupons",title:(0,o.__)("Coupons","woocommerce"),component:p,navArgs:{id:"woocommerce-analytics-coupons"}},{report:"taxes",title:(0,o.__)("Taxes","woocommerce"),component:m,navArgs:{id:"woocommerce-analytics-taxes"}},"yes"===y?{report:"stock",title:(0,o.__)("Stock","woocommerce"),component:f,navArgs:{id:"woocommerce-analytics-stock"}}:null,{report:"customers",title:(0,o.__)("Customers","woocommerce"),component:g},{report:"downloads",title:(0,o.__)("Downloads","woocommerce"),component:h,navArgs:{id:"woocommerce-analytics-downloads"}}].filter(Boolean);return(0,n.applyFilters)("woocommerce_admin_reports_list",e)}},15838:(e,t,r)=>{"use strict";r.d(t,{gV:()=>d,$W:()=>g});var o=r(27723),n=r(52619),a=r(36849),s=r(98846),i=r(40314),c=r(77374),l=r(39793);var u=r(56109);const d=["processing","on-hold"],p=["completed","processing","refunded","cancelled","failed","pending","on-hold"],m=Object.keys(u.wm).filter((e=>"refunded"!==e)).map((e=>({value:e,label:u.wm[e],description:(0,o.sprintf)((0,o.__)("Exclude the %s status from reports","woocommerce"),u.wm[e])}))),h=(0,u.Qk)("unregisteredOrderStatuses",{}),f=[{key:"defaultStatuses",options:m.filter((e=>p.includes(e.value)))},{key:"customStatuses",label:(0,o.__)("Custom Statuses","woocommerce"),options:m.filter((e=>!p.includes(e.value)))},{key:"unregisteredStatuses",label:(0,o.__)("Unregistered Statuses","woocommerce"),options:Object.keys(h).map((e=>({value:e,label:e,description:(0,o.sprintf)((0,o.__)("Exclude the %s status from reports","woocommerce"),e)})))}],g=(0,n.applyFilters)("woocommerce_admin_analytics_settings",{woocommerce_excluded_report_order_statuses:{label:(0,o.__)("Excluded statuses:","woocommerce"),inputType:"checkboxGroup",options:f,helpText:(0,a.A)({mixedString:(0,o.__)("Orders with these statuses are excluded from the totals in your reports. The {{strong}}Refunded{{/strong}} status can not be excluded.","woocommerce"),components:{strong:(0,l.jsx)("strong",{})}}),defaultValue:["pending","cancelled","failed"]},woocommerce_actionable_order_statuses:{label:(0,o.__)("Actionable statuses:","woocommerce"),inputType:"checkboxGroup",options:f,helpText:(0,o.__)("Orders with these statuses require action on behalf of the store admin. These orders will show up in the Home Screen - Orders task.","woocommerce"),defaultValue:d},woocommerce_default_date_range:{name:"woocommerce_default_date_range",label:(0,o.__)("Default date range:","woocommerce"),inputType:"component",component:({value:e,onChange:t})=>{const{wcAdminSettings:r}=(0,i.useSettings)("wc_admin",["wcAdminSettings"]),{woocommerce_default_date_range:o}=r,n=Object.fromEntries(new URLSearchParams(e.replace(/&amp;/g,"&"))),{period:a,compare:u,before:d,after:p}=(0,c.getDateParamsFromQuery)(n,o),{primary:m,secondary:h}=(0,c.getCurrentDates)(n,o),f={period:a,compare:u,before:d,after:p,primaryDate:m,secondaryDate:h};return(0,l.jsx)(s.DateRangeFilterPicker,{query:n,onRangeSelect:e=>{t({target:{name:"woocommerce_default_date_range",value:new URLSearchParams(e).toString()}})},dateQuery:f,isoDateFormat:c.isoDateFormat})},helpText:(0,o.__)("Select a default date range. When no range is selected, reports will be viewed by the default date range.","woocommerce"),defaultValue:"period=month&compare=previous_year"},woocommerce_date_type:{name:"woocommerce_date_type",label:(0,o.__)("Date type:","woocommerce"),inputType:"select",options:[{label:(0,o.__)("Select a date type","woocommerce"),value:"",disabled:!0},{label:(0,o.__)("Date created","woocommerce"),value:"date_created",key:"date_created"},{label:(0,o.__)("Date paid","woocommerce"),value:"date_paid",key:"date_paid"},{label:(0,o.__)("Date completed","woocommerce"),value:"date_completed",key:"date_completed"}],helpText:(0,o.__)("Database date field considered for Revenue and Orders reports","woocommerce")}})},3246:(e,t,r)=>{"use strict";r.d(t,{d:()=>l});var o=r(14908),n=r(36849),a=r(98846),s=r(83306),i=r(27723),c=r(39793);const l=({textProps:e,message:t,eventName:r="",eventProperties:l={},targetUrl:u,linkType:d="wc-admin",target:p,onClickCallback:m})=>{const h=t.match(/{{Link}}(.*?){{\/Link}}/),f=h?h[1]:"",g="external"===d&&"_blank"===p;return(0,c.jsx)(o.Text,{...e,children:(0,n.A)({mixedString:t,components:{Link:(0,c.jsx)(a.Link,{onClick:()=>{if(m?m():(0,s.recordEvent)(r,l),"external"!==d)return window.location.href=u,!1},href:u,type:d,target:g?"_blank":void 0,"aria-label":g?`${f} (${(0,i.__)("opens in a new tab","woocommerce")})`:void 0})}})})}},29332:(e,t,r)=>{"use strict";r.d(t,{G9:()=>c,VJ:()=>s,e9:()=>i,xC:()=>a});var o=r(40314),n=r(15838);function a(e,t){if(!t.length)return 0;const r={page:1,per_page:1,status:t,_fields:["id"]},{getItemsTotalCount:n,getItemsError:a,isResolving:s}=e(o.itemsStore),i=n("orders",r,null),c=Boolean(a("orders",r)),l=s("getItemsTotalCount",["orders",r,null]);return c||l?null:i}function s(e){const{getSetting:t}=e(o.settingsStore),{woocommerce_actionable_order_statuses:r=n.gV}=t("wc_admin","wcAdminSettings",{});return r}const i={status:"publish"};function c(e){const{getItemsTotalCount:t,getItemsError:r,isResolving:n}=e(o.itemsStore),a=null,s=t("products/count-low-in-stock",i,a),c=Boolean(r("products/count-low-in-stock",i)),l=n("getItemsTotalCount",["products/count-low-in-stock",i,a]);return c||l&&s===a?null:s}},42288:(e,t,r)=>{"use strict";r.d(t,{D9:()=>n,YY:()=>a,my:()=>s});var o=r(40314);const n=5,a={page:1,per_page:1,status:"hold",_embed:1,_fields:["id"]};function s(e){const{getReviewsTotalCount:t,getReviewsError:r,isResolving:n}=e(o.reviewsStore),s=t(a),i=Boolean(r(a)),c=n("getReviewsTotalCount",[a]);return i||c&&void 0===s?null:s}},99915:(e,t,r)=>{"use strict";r.d(t,{EM:()=>c,Oh:()=>s,fK:()=>p});var o=r(47143),n=r(40314),a=r(56109);const s=e=>(0,a.Qk)("visibleTaskListIds",[]).includes(e),i=e=>(0,a.Qk)("completedTaskListIds",[]).includes(e),c=e=>s(e)&&!i(e),l=()=>({requestingTaskListOptions:!1,setupTaskListHidden:!s("setup"),setupTaskListComplete:i("setup"),setupTaskListActive:c("setup"),setupTasksCount:void 0,setupTasksCompleteCount:void 0,thingsToDoNextCount:void 0}),u=e=>{const{getTaskList:t,hasFinishedResolution:r}=e,o=t("setup"),a=(0,n.getVisibleTasks)(o?.tasks||[]),s=!o||o.isHidden,i=o?.isComplete;return{setupTaskListHidden:s,setupTaskListComplete:i,setupTaskListActive:!s&&!i,setupTasksCount:a.length,setupTasksCompleteCount:a.filter((e=>e.isComplete)).length,requestingTaskListOptions:!r("getTaskLists")}},d=e=>{const{getTaskList:t,hasFinishedResolution:r}=e;return{thingsToDoNextCount:(o=t("extended"),o&&o.tasks.length&&!o.isHidden?o.tasks.filter((e=>e.canView&&!e.isComplete&&!e.isDismissed)).length:0),requestingTaskListOptions:!r("getTaskLists")};var o},p=({setupTasklist:e,extendedTaskList:t}={setupTasklist:!0,extendedTaskList:!0})=>{const r=e&&c("setup"),a=t&&c("extended");return(0,o.useSelect)((e=>{if(!r&&!a)return l();const t=e(n.ONBOARDING_STORE_NAME);return r?a?{...l(),...u(t),...d(t)}:{...l(),...u(t)}:{...l(),...d(t)}}),[r,a])}},46591:(e,t,r)=>{"use strict";r.d(t,{e8:()=>i,kT:()=>s,yz:()=>u});var o=r(66087),n=r(30155),a=r.n(n);function s(e,t){return(0,o.filter)(e,(e=>{const{is_deleted:r,date_created_gmt:o,status:n}=e;if(!r)return(!t||!o||new Date(o+"Z").getTime()>t)&&"unactioned"===n})).length}function i(e){return(0,o.filter)(e,(e=>{const{is_deleted:t}=e;return!t})).length>0}const c=(e,t,r=" ")=>{let o=e.slice(0,t);if(e.indexOf(r,t)!==t){const e=o.lastIndexOf(r);e>-1&&(o=o.slice(0,e))}return o.join("")},l=(e,t)=>{const r=document.createElement("div"),o=Array.from(e.childNodes),n=new(a());let s=0;for(let e=0;e<o.length;e++){let a=o[e].cloneNode(!0);const i=n.splitGraphemes(a.textContent);if(s+i.length<=t){r.appendChild(a),s+=i.length;continue}const u=t-s;a.hasChildNodes()?a=l(a,u):a.textContent=c(i,u),r.appendChild(a);break}return r},u=(e,t)=>{const r=document.createElement("div"),o=new(a());return r.innerHTML=e,o.splitGraphemes(r.textContent).length>t?l(r,t).innerHTML+"...":e}},11357:(e,t,r)=>{"use strict";r.d(t,{N:()=>u});var o=r(86087),n=r(27723),a=r(56427),s=r(98846),i=r(14908),c=r(45155),l=r(39793);const u=()=>{const[e,t]=(0,o.useState)(!0);return(0,o.useEffect)((()=>{const e=setTimeout((()=>{t(!1)}),3e3);return()=>{clearTimeout(e)}}),[]),e?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(c.WooHeaderPageTitle,{children:(0,n.__)("Loading…","woocommerce")}),(0,l.jsx)("div",{className:"woocommerce-layout__loading",children:(0,l.jsx)(s.Spinner,{})})]}):(0,l.jsx)("div",{className:"woocommerce-layout__no-match",children:(0,l.jsx)(a.Card,{children:(0,l.jsx)(a.CardBody,{children:(0,l.jsx)(i.Text,{children:(0,n.__)("Sorry, you are not allowed to access this page.","woocommerce")})})})})}},11315:(e,t,r)=>{"use strict";r.d(t,{y:()=>n});var o=r(86817);const n=()=>{(0,o.init)({errorRateLimitMs:6e4})}},46772:(e,t,r)=>{"use strict";r.d(t,{R:()=>n});var o=r(47143);function n(e){const{createNotice:t}=(0,o.dispatch)("core/notices");e.error_data&&e.errors&&Object.keys(e.errors).length?Object.keys(e.errors).forEach((r=>{t("error",e.errors[r].join(" "))})):e.message&&t(e.code?"error":"success",e.message)}},10434:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});const o={SET_INSTALLED_PLUGINS:"SET_INSTALLED_PLUGINS",SET_ACTIVATING_PLUGIN:"SET_ACTIVATING_PLUGIN",REMOVE_ACTIVATING_PLUGIN:"REMOVE_ACTIVATING_PLUGIN",SET_RECOMMENDED_PLUGINS:"SET_RECOMMENDED_PLUGINS",INSTALL_AND_ACTIVATE_RECOMMENDED_PLUGIN:"INSTALL_AND_ACTIVATE_RECOMMENDED_PLUGIN",SET_BLOG_POSTS:"SET_BLOG_POSTS",SET_MISC_RECOMMENDATIONS:"SET_MISC_RECOMMENDATIONS",SET_ERROR:"SET_ERROR"}},23461:(e,t,r)=>{"use strict";r.r(t),r.d(t,{activateInstalledPlugin:()=>y,handleFetchError:()=>h,installAndActivateRecommendedPlugin:()=>w,loadInstalledPluginsAfterActivation:()=>g,receiveActivatingPlugin:()=>l,receiveBlogPosts:()=>m,receiveInstalledPlugins:()=>c,receiveMiscRecommendations:()=>p,receiveRecommendedPlugins:()=>d,removeActivatingPlugin:()=>u,setError:()=>f});var o=r(66161),n=r(47143),a=r(27723),s=r(10434),i=r(17143);function c(e){return{type:s.A.SET_INSTALLED_PLUGINS,plugins:e}}function l(e){return{type:s.A.SET_ACTIVATING_PLUGIN,pluginSlug:e}}function u(e){return{type:s.A.REMOVE_ACTIVATING_PLUGIN,pluginSlug:e}}function d(e,t){return{type:s.A.SET_RECOMMENDED_PLUGINS,data:{plugins:e,category:t}}}function p(e){return{type:s.A.SET_MISC_RECOMMENDATIONS,data:{miscRecommendations:e}}}function m(e,t){return{type:s.A.SET_BLOG_POSTS,data:{posts:e,category:t}}}function h(e,t){const{createNotice:r}=(0,n.dispatch)("core/notices");r("error",t),console.log(e)}function f(e,t){return{type:s.A.SET_ERROR,category:e,error:t}}function*g(e){try{const t=yield(0,o.apiFetch)({path:`${i.R}/overview/installed-plugins`});if(!t)throw new Error;yield c(t),yield u(e)}catch(e){yield h(e,(0,a.__)("There was an error loading installed extensions.","woocommerce"))}}function*y(e){const{createNotice:t}=(0,n.dispatch)("core/notices");yield l(e);try{if(!(yield(0,o.apiFetch)({path:i.R+"/overview/activate-plugin",method:"POST",data:{plugin:e}})))throw new Error;yield t("success",(0,a.__)("The extension has been successfully activated.","woocommerce")),yield g(e)}catch(t){yield h(t,(0,a.__)("There was an error trying to activate the extension.","woocommerce")),yield u(e)}return!0}function*w(e,t){return{type:s.A.INSTALL_AND_ACTIVATE_RECOMMENDED_PLUGIN,data:{pluginSlug:e,category:t}}}},17143:(e,t,r)=>{"use strict";r.d(t,{R:()=>n,U:()=>o});const o="wc/marketing",n="/wc-admin/marketing"},30502:(e,t,r)=>{"use strict";r.d(t,{M:()=>u});var o=r(47143),n=r(66161),a=r(17143),s=r(23461),i=r(30924),c=r(64833),l=r(99834);const u=(0,o.createReduxStore)(a.U,{actions:s,selectors:i,resolvers:c,controls:n.controls,reducer:l.A});(0,o.register)(u)},99834:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var o=r(66087),n=r(10434),a=r(56109);const{installedExtensions:s}=(0,a.Qk)("marketing",{}),i={installedPlugins:s,activatingPlugins:[],recommendedPlugins:{},miscRecommendations:[],blogPosts:{},errors:{}},c=(e=i,t)=>{switch(t.type){case n.A.SET_INSTALLED_PLUGINS:return{...e,installedPlugins:t.plugins};case n.A.SET_ACTIVATING_PLUGIN:return{...e,activatingPlugins:[...e.activatingPlugins,t.pluginSlug]};case n.A.REMOVE_ACTIVATING_PLUGIN:return{...e,activatingPlugins:(0,o.without)(e.activatingPlugins,t.pluginSlug)};case n.A.SET_RECOMMENDED_PLUGINS:return{...e,recommendedPlugins:{...e.recommendedPlugins,[t.data.category]:t.data.plugins}};case n.A.INSTALL_AND_ACTIVATE_RECOMMENDED_PLUGIN:const r=e.recommendedPlugins[t.data.category]?.filter((e=>e.product!==t.data.pluginSlug));return{...e,recommendedPlugins:{...e.recommendedPlugins,[t.data.category]:r}};case n.A.SET_BLOG_POSTS:return{...e,blogPosts:{...e.blogPosts,[t.data.category]:t.data.posts}};case n.A.SET_MISC_RECOMMENDATIONS:return{...e,miscRecommendations:t.data.miscRecommendations};case n.A.SET_ERROR:return{...e,errors:{...e.errors,blogPosts:{...e.errors.blogPosts,[t.category]:t.error}}};default:return e}}},64833:(e,t,r)=>{"use strict";r.r(t),r.d(t,{getBlogPosts:()=>l,getMiscRecommendations:()=>c,getRecommendedPlugins:()=>i});var o=r(27723),n=r(66161),a=r(23461),s=r(17143);function*i(e){try{const t=yield e?`&category=${e}`:"",r=yield(0,n.apiFetch)({path:`${s.R}/recommended?per_page=50${t}`});if(!r)throw new Error;yield(0,a.receiveRecommendedPlugins)(r,e)}catch(e){yield(0,a.handleFetchError)(e,(0,o.__)("There was an error loading recommended extensions.","woocommerce"))}}function*c(){try{const e=yield(0,n.apiFetch)({path:`${s.R}/misc-recommendations`});if(!e)throw new Error;yield(0,a.receiveMiscRecommendations)(e)}catch(e){yield(0,a.handleFetchError)(e,(0,o.__)("There was an error loading misc recommendations","woocommerce"))}}function*l(e){try{const t=yield e?`?category=${e}`:"",r=yield(0,n.apiFetch)({path:`${s.R}/knowledge-base${t}`,method:"GET"});if(!r)throw new Error;yield(0,a.receiveBlogPosts)(r,e)}catch(t){yield(0,a.setError)(e,t)}}},30924:(e,t,r)=>{"use strict";function o(e){return e.installedPlugins}function n(e){return e.activatingPlugins}function a(e,t){return e.recommendedPlugins[t]||[]}function s(e){return e.miscRecommendations}function i(e,t){return e.blogPosts[t]||[]}function c(e,t){return e.errors.blogPosts&&e.errors.blogPosts[t]}r.r(t),r.d(t,{getActivatingPlugins:()=>n,getBlogPosts:()=>i,getBlogPostsError:()=>c,getInstalledPlugins:()=>o,getMiscRecommendations:()=>s,getRecommendedPlugins:()=>a})},11488:(e,t,r)=>{"use strict";r.d(t,{$Y:()=>m,H2:()=>a,K_:()=>f,OA:()=>c,UD:()=>p,XA:()=>u,dL:()=>h,kJ:()=>i,mz:()=>n,q$:()=>s,rx:()=>l,t0:()=>d});var o=r(56109);const n="discover",a="https://woocommerce.com",s="/extensions",i="/wp-json/wccom-extensions/1.0/search",c="/wp-json/wccom-extensions/1.0/categories",l="/wp-json/wccom-extensions/1.0/iam-settings",u=a+"/cart/",d=a+"/my-account/my-subscriptions/",p=a+"/document/managing-woocommerce-com-subscriptions/#transfer-a-woocommerce-com-subscription",m=a+"/document/managing-woocommerce-com-subscriptions/#share-a-subscription",h=o.kY+"plugins.php",f=a+"/product-download/woo-update-manager"},22480:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>w,TF:()=>g,bb:()=>h,KH:()=>f});var o=r(27723),n=r(72744),a=r(5573),s=r(39793);const i=(0,s.jsx)(a.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)(a.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M6.68822 16.625L5.5 17.8145L5.5 5.5L18.5 5.5L18.5 16.625L6.68822 16.625ZM7.31 18.125L19 18.125C19.5523 18.125 20 17.6773 20 17.125L20 5C20 4.44772 19.5523 4 19 4H5C4.44772 4 4 4.44772 4 5V19.5247C4 19.8173 4.16123 20.086 4.41935 20.2237C4.72711 20.3878 5.10601 20.3313 5.35252 20.0845L7.31 18.125ZM16 9.99997H8V8.49997H16V9.99997ZM8 14H13V12.5H8V14Z"})}),c=(0,s.jsx)(a.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(a.Path,{d:"M12 3.176l6.75 3.068v4.574c0 3.9-2.504 7.59-6.035 8.755a2.283 2.283 0 01-1.43 0c-3.53-1.164-6.035-4.856-6.035-8.755V6.244L12 3.176zM6.75 7.21v3.608c0 3.313 2.145 6.388 5.005 ************.331.053.49 0 2.86-.942 5.005-4.017 5.005-7.33V7.21L12 4.824 6.75 7.21z",fillRule:"evenodd",clipRule:"evenodd"})}),l=(0,s.jsx)(a.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(a.Path,{d:"M15.5 9.5a1 1 0 100-2 1 1 0 000 2zm0 1.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5zm-2.25 6v-2a2.75 2.75 0 00-2.75-2.75h-4A2.75 2.75 0 003.75 15v2h1.5v-2c0-.69.56-1.25 1.25-1.25h4c.69 0 1.25.56 1.25 1.25v2h1.5zm7-2v2h-1.5v-2c0-.69-.56-1.25-1.25-1.25H15v-1.5h2.5A2.75 2.75 0 0120.25 15zM9.5 8.5a1 1 0 11-2 0 1 1 0 012 0zm1.5 0a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z",fillRule:"evenodd"})});var u=r(24148);function d(e){const{icon:t,title:r,description:o}=e;return(0,s.jsxs)("div",{className:"woocommerce-marketplace__icon-group",children:[(0,s.jsxs)("div",{className:"woocommerce-marketplace__icon-group-headline",children:[(0,s.jsx)(u.A,{icon:t,size:20,className:"woocommerce-marketplace__icon-group-icon"}),(0,s.jsx)("h3",{className:"woocommerce-marketplace__icon-group-title",children:r})]}),(0,s.jsx)("p",{className:"woocommerce-marketplace__icon-group-description",children:o})]})}var p=r(11488),m=r(3246);const h=e=>(0,s.jsx)(m.d,{targetUrl:p.H2+"/refund-policy/",linkType:"external",eventName:`marketplace_${e}_link_click`,eventProperties:{feature_clicked:"money_back_guarantee"},message:(0,o.__)("30-day {{Link}}money-back guarantee{{/Link}}","woocommerce"),target:"_blank"}),f=e=>(0,s.jsx)(m.d,{targetUrl:p.H2+"/docs/",linkType:"external",eventName:`marketplace_${e}_link_click`,eventProperties:{feature_clicked:"get_help"},message:(0,o.__)("{{Link}}Get help{{/Link}} when you need it","woocommerce"),target:"_blank"}),g=e=>(0,s.jsx)(m.d,{targetUrl:p.H2+"/products/",linkType:"external",eventName:`marketplace_${e}_link_click`,eventProperties:{feature_clicked:"products_you_can_trust"},message:(0,o.__)("{{Link}}Products{{/Link}} you can trust","woocommerce"),target:"_blank"});function y(){return(0,s.jsxs)("div",{className:"woocommerce-marketplace__footer-content",children:[(0,s.jsx)("h2",{className:"woocommerce-marketplace__footer-title",children:(0,o.__)("Hundreds of vetted products and services. Unlimited potential.","woocommerce")}),(0,s.jsxs)("div",{className:"woocommerce-marketplace__footer-columns",children:[(0,s.jsx)(d,{icon:n.A,title:h("footer"),description:(0,o.__)("If you change your mind within 30 days of your purchase, we'll give you a full refund — hassle-free.","woocommerce")}),(0,s.jsx)(d,{icon:i,title:f("footer"),description:(0,o.__)("With detailed documentation and a global support team, help is always available if you need it.","woocommerce")}),(0,s.jsx)(d,{icon:c,title:g("footer"),description:(0,o.__)("Everything in the Marketplace has been built by our own team or by our trusted partners, so you can be sure of its quality.","woocommerce")}),(0,s.jsx)(d,{icon:l,title:(0,o.__)("Support the ecosystem","woocommerce"),description:(0,o.__)("Our team and partners are continuously improving your extensions, themes, and WooCommerce experience.","woocommerce")})]})]})}function w(){return(0,s.jsx)("div",{className:"woocommerce-marketplace__footer",children:(0,s.jsx)(y,{})})}},80742:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var o=r(86087),n=r(56427),a=r(27723),s=r(39793);function i(e){const{setIsModalOpen:t,disconnectURL:r}=e,[i,c]=(0,o.useState)(!1),l=()=>t(!1);return(0,s.jsxs)(n.Modal,{title:(0,a.__)("Are you sure you want to disconnect?","woocommerce"),onRequestClose:l,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal",style:{borderRadius:4},size:"medium",overlayClassName:"woocommerce-marketplace__header-account-modal-overlay",children:[(0,s.jsx)("p",{className:"woocommerce-marketplace__header-account-modal-text",children:(0,a.__)("Keep your store connected to WooCommerce.com to get updates, manage your subscriptions, and receive streamlined support for your extensions and themes.","woocommerce")}),(0,s.jsxs)(n.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group",children:[(0,s.jsx)(n.Button,{variant:"tertiary",href:r,onClick:()=>c(!i),isBusy:i,isDestructive:!0,className:"woocommerce-marketplace__header-account-modal-button",children:(0,a.__)("Disconnect","woocommerce")}),(0,s.jsx)(n.Button,{variant:"primary",onClick:l,className:"woocommerce-marketplace__header-account-modal-button",children:(0,a.__)("Keep connected","woocommerce")})]})]})}},10790:(e,t,r)=>{"use strict";r.d(t,{A:()=>y});var o=r(86087),n=r(56427),a=r(95687),s=r(24148),i=r(6513),c=r(16254),l=r(27723),u=r(83306),d=r(56109),p=r(80742),m=r(11488),h=r(95893),f=r(39793);const g=e=>(0,f.jsx)(n.MenuItem,{...e});function y({page:e="wc-admin"}){var t,r;const[y,w]=(0,o.useState)(!1),[v,b]=(0,o.useState)(!1),_=(0,d.Qk)("wccomHelper",{}),x=null!==(t=_?.isConnected)&&void 0!==t&&t,j=(0,h.E4)(e),M=_?.userEmail,S=null!==(r=_?.userAvatar)&&void 0!==r?r:a.A,A=m.H2+"/my-dashboard/",C=x?A:j,k=x?(0,l.__)("Connected to WooCommerce.com","woocommerce"):(0,l.__)("Connect to WooCommerce.com","woocommerce");return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(n.DropdownMenu,{className:"woocommerce-layout__activity-panel-tab woocommerce-marketplace__user-menu",icon:!x||v?a.A:(0,f.jsx)("img",{src:S,alt:"",className:"woocommerce-marketplace__menu-avatar-image",onError:()=>b(!0)}),label:(0,l.__)("User options","woocommerce"),toggleProps:{className:"woocommerce-layout__activity-panel-tab",onClick:()=>(0,u.recordEvent)("header_account_click",{page:e})},popoverProps:{className:"woocommerce-layout__activity-panel-popover"},children:()=>(0,f.jsxs)(f.Fragment,{children:[(0,f.jsxs)(n.MenuGroup,{className:"woocommerce-layout__homescreen-display-options",label:k,children:[(0,f.jsx)(g,{className:"woocommerce-marketplace__menu-item",href:C,onClick:()=>{x?(0,u.recordEvent)("header_account_view_click",{page:e}):(0,u.recordEvent)("header_account_connect_click",{page:e})},children:x?(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(s.A,{icon:a.A,size:24,className:"woocommerce-marketplace__menu-icon"}),(0,f.jsx)("span",{className:"woocommerce-marketplace__main-text",children:M})]}):(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(s.A,{icon:a.A,size:24,className:"woocommerce-marketplace__menu-icon"}),(0,f.jsxs)("div",{className:"woocommerce-marketplace__menu-text",children:[(0,l.__)("Connect account","woocommerce"),(0,f.jsx)("span",{className:"woocommerce-marketplace__sub-text",children:(0,l.__)("Get product updates, manage your subscriptions from your store admin, and get streamlined support.","woocommerce")})]})]})}),"wc-addons"===e&&!x&&(0,f.jsxs)(g,{href:A,onClick:()=>(0,u.recordEvent)("header_account_view_click",{page:e}),children:[(0,f.jsx)(s.A,{icon:i.A,size:24,className:"woocommerce-marketplace__menu-icon"}),(0,l.__)("WooCommerce.com account","woocommerce")]})]}),x&&(0,f.jsx)(n.MenuGroup,{className:"woocommerce-layout__homescreen-display-options",children:(0,f.jsxs)(g,{onClick:()=>{(0,u.recordEvent)("header_account_disconnect_click",{page:e}),w(!0)},children:[(0,f.jsx)(s.A,{icon:c.A,size:24,className:"woocommerce-marketplace__menu-icon"}),(0,l.__)("Disconnect account","woocommerce")]})})]})}),y&&(0,f.jsx)(p.A,{setIsModalOpen:w,disconnectURL:j})]})}},15001:(e,t,r)=>{"use strict";r.d(t,{If:()=>a,WX:()=>n,ch:()=>o});let o=function(e){return e.theme="theme",e.extension="extension",e.businessService="business-service",e}({}),n=function(e){return e.compact="compact",e.regular="regular",e}({}),a=function(e){return e.theme="theme",e.extension="extension",e.businessService="business-service",e.all="all",e}({})},65287:(e,t,r)=>{"use strict";r.d(t,{E:()=>a});var o=r(47143);const n={notices:{}},a=(0,o.createReduxStore)("woocommerce-admin/subscription-notices",{reducer(e=n,t){switch(t.type){case"ADD_NOTICE":return{...e,notices:{...e.notices,[t.productKey]:{productKey:t.productKey,message:t.message,status:t.status,options:t.options}}};case"REMOVE_NOTICE":const r={...e.notices};return r[t.productKey]&&delete r[t.productKey],{...e,notices:r}}return e},actions:{addNotice:(e,t,r,o)=>({type:"ADD_NOTICE",productKey:e,message:t,status:r,options:o}),removeNotice:e=>({type:"REMOVE_NOTICE",productKey:e})},selectors:{notices:e=>e?Object.values(e.notices):[],getNotice(e,t){if(e)return e.notices[t]}}});(0,o.register)(a)},29248:(e,t,r)=>{"use strict";r.d(t,{T:()=>o});let o=function(e){return e.Success="success",e.Error="error",e}({})},95893:(e,t,r)=>{"use strict";r.d(t,{A5:()=>R,CW:()=>k,E4:()=>V,Hu:()=>b,LK:()=>F,Mp:()=>O,QC:()=>x,SI:()=>z,UN:()=>I,Xs:()=>E,YW:()=>j,b8:()=>_,dV:()=>T,e2:()=>C,fj:()=>D,hP:()=>S,jE:()=>M,vc:()=>P,w5:()=>L,wX:()=>U,wu:()=>A});var o=r(1455),n=r.n(o),a=r(27723),s=r(47143),i=r(692),c=r(56427),l=r(56109),u=r(11488),d=r(15001),p=r(29248),m=r(65287),h=r(39793);const f=100,g=new Map;function y(){for(;g.size>f;)g.delete(g.keys().next().value)}async function w(e){const t=JSON.stringify(e);return g.get(t)?new Promise((e=>{e(g.get(t))})):new Promise(((r,o)=>{n()(e).then((e=>{g.set(t,e),y(),r(e)})).catch((()=>{o()}))}))}async function v(e,t){return g.get(e)?new Promise((t=>{t(g.get(e))})):new Promise(((r,o)=>{fetch(e,{signal:t}).then((e=>{if(!e.ok)throw new Error(e.statusText);return e.json()})).then((t=>{g.set(e,t),y(),r(t)})).catch((()=>{o()}))}))}async function b(e,t){l.ne.userLocale&&!e.get("locale")&&e.set("locale",l.ne.userLocale);const r=u.H2+u.kJ+"?"+e.toString();return new Promise(((e,o)=>{v(r,t).then((t=>{const r=t.products.map((e=>{var t,r,o;return{id:e.id,slug:e.slug,title:e.title,image:e.image,type:e.type,freemium_type:e.freemium_type,description:e.excerpt,vendorName:e.vendor_name,vendorUrl:e.vendor_url,icon:e.icon,url:e.link,price:null!==(t=e.raw_price)&&void 0!==t?t:e.price,regularPrice:e.regular_price,isOnSale:e.is_on_sale,averageRating:null!==(r=e.rating)&&void 0!==r?r:null,reviewsCount:null!==(o=e.reviews_count)&&void 0!==o?o:null,isInstallable:e.is_installable,featuredImage:e.featured_image,productCategory:e.product_category,color:e.color,billingPeriod:e.billing_period,billingPeriodInterval:e.billing_period_interval,currency:e.currency}})),o=t.total_pages,n=t.total_products;e({products:r,totalPages:o,totalProducts:n})})).catch(o)}))}async function _(){let e="/wc/v3/marketplace/featured";l.ne.userLocale&&(e=`${e}?locale=${l.ne.userLocale}`);try{return await w({path:e.toString()})}catch(e){return[]}}async function x(e){let t=`/wc/v1/marketplace/product-preview?product_id=${e}`;l.ne.userLocale&&(t=`${t}&locale=${l.ne.userLocale}`);try{return await w({path:t.toString()})}catch(e){return{data:{html:"",css:""}}}}function j(e){switch(e){case"themes":return d.ch.theme;case"business-services":return d.ch.businessService;default:return d.ch.extension}}function M(e){const t=new URL(u.H2+u.OA);return l.ne.userLocale&&t.searchParams.set("locale",l.ne.userLocale),e===d.ch.theme?t.searchParams.set("parent","themes"):e===d.ch.businessService&&t.searchParams.set("parent","business-services"),v(t.toString()).then((e=>e)).catch((()=>[]))}async function S(){return await n()({path:"/wc/v3/marketplace/subscriptions".toString()})}async function A(){return await n()({path:"/wc/v3/marketplace/refresh".toString(),method:"POST"})}function C(e){if(!0===e.active)return Promise.resolve();const t=new URLSearchParams;return t.append("product_key",e.product_key),n()({path:"/wc/v3/marketplace/subscriptions/connect".toString(),method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:t})}function k(e){if(!0===e.active)return Promise.resolve();const t=new URLSearchParams;return t.append("product_key",e.product_key),n()({path:"/wc/v3/marketplace/subscriptions/activate-plugin".toString(),method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:t})}function N(e,t){return new Promise(((r,o)=>{window.wp.updates?window.wp.updates.ajax(e,{...t,success:e=>{r({success:!0,data:e})},error:e=>{o({success:!1,data:{message:e.errorMessage}})}}):o((0,a.__)("Please reload and try again","woocommerce"))}))}function E(e){return n()({path:"/wc/v3/marketplace/subscriptions/install-url?product_key="+e.product_key}).then((e=>e?.data.url))}function I(e,t){return N("install-"+e,{slug:t})}function T(e){return C(e).then((()=>I(e.product_type,e.zip_slug).then((()=>function(e){if(!0===e.local.active)return Promise.resolve();const t=new URLSearchParams;return t.append("product_key",e.product_key),n()({path:"/wc/v3/marketplace/subscriptions/activate".toString(),method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:t}).then((()=>Promise.resolve())).catch((()=>Promise.reject({success:!1,data:{message:(0,a.sprintf)((0,a.__)("%s could not be activated. Please activate it manually.","woocommerce"),e.product_name)}})))}(e))).catch((t=>function(e){if(!1===e.active)return Promise.resolve();const t=new URLSearchParams;return t.append("product_key",e.product_key),n()({path:"/wc/v3/marketplace/subscriptions/disconnect".toString(),method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:t})}(e).finally((()=>Promise.reject(t)))))))}function P(e){return N("update-"+e.product_type,{slug:e.local.slug,[e.product_type]:e.local.path})}function O(e,t,r,o){r===p.T.Error?(0,s.dispatch)(m.E).addNotice(e,t,r,o):(o?.icon||(o={...o,icon:(0,h.jsx)(c.Icon,{icon:"saved"})}),(0,s.dispatch)(i.store).createSuccessNotice(t,o))}const D=e=>{(0,s.dispatch)(m.E).removeNotice(e)},L=e=>({id:e.product_id,title:e.product_name,image:"",type:e.product_type,description:"",vendorName:"",vendorUrl:"",icon:e.product_icon,url:e.product_url,price:-1,regularPrice:-1,isOnSale:!1,averageRating:null,reviewsCount:null,isInstallable:!1,currency:""}),R=(e,t)=>{if(!e)return e;const r=new URL(e);return r?(t.forEach((([e,t])=>{r.searchParams.set(e,t)})),r.toString()):e},z=e=>e.product_key?R(u.t0,[["key",e.product_key.toString()]]):u.t0,F=e=>R(u.XA,[["renew_product",e.product_id.toString()],["product_key",e.product_key],["order_id",e.order_id.toString()]]),U=e=>R(u.XA,[["add-to-cart",e.product_id.toString()]]),V=(e="wc-admin",t=!1)=>{const r=(0,l.Qk)("wccomHelper",{});if(!t&&!r.connectURL)return"";if(t&&!r.reConnectURL)return"";const o=t?r.reConnectURL:r.connectURL,n=new URL(window.location.href);return n.searchParams.set("page",e),R(o,[["redirect_admin_url",encodeURIComponent(n.toString())],["page",e]])}},76861:(e,t,r)=>{"use strict";r.d(t,{mg:()=>g,Tv:()=>v,iR:()=>b});var o=r(56427),n=r(47143),a=r(86087),s=r(28180),i=r(27723),c=r(14908),l=r(83306),u=r(96476),d=r(40314),p=r(15703),m=r(17143);r(30502);const h="order_attribution_install_banner_dismissed",f="woocommerce_remote_variant_assignment",g="order-attribution-install-banner-header";var y=r(46772),w=r(39793);const v=({bannerImage:e=null,bannerType:t="order-attribution-install-banner-big",eventContext:r="analytics-overview",dismissable:v=!1,badgeText:b="",title:_="",description:x="",buttonText:j=""})=>{const[M,S]=(0,a.useState)(!1),{isDismissed:A,dismiss:C,shouldShowBanner:k}=(({isInstalling:e})=>{const{currentUserCan:t}=(0,d.useUser)(),{[h]:r,updateUserPreferences:o}=(0,d.useUserPreferences)(),{canUserInstallPlugins:s,orderAttributionInstallState:i}=(0,n.useSelect)((e=>{const{getPluginInstallState:r}=e(d.pluginsStore);return{orderAttributionInstallState:r("woocommerce-analytics"),canUserInstallPlugins:t("install_plugins")}}),[t]),{loading:c,isBannerDismissed:g,remoteVariantAssignment:y}=(0,n.useSelect)((e=>{const{getOption:t,hasFinishedResolution:o}=e(d.optionsStore);return{loading:!o("getOption",[f]),isBannerDismissed:r,remoteVariantAssignment:t(f)}}),[r]),{loadingRecommendations:w,recommendations:v}=(0,n.useSelect)((e=>{const{getMiscRecommendations:t,hasFinishedResolution:r}=e(m.U);return{loadingRecommendations:!s||!r("getMiscRecommendations"),recommendations:s?t():[]}}),[s]),b=(0,a.useMemo)((()=>{if(w||!Array.isArray(v)||0===v.length)return null;for(const e of v)if("woocommerce-analytics"===e.id)return e?.order_attribution_promotion_percentage||null;return null}),[w,v]),_=(0,a.useCallback)((()=>!(!s||c)&&(!!e||!["installed","activated"].includes(i)&&((e,t)=>{if(e=parseInt(e,10),isNaN(e))return!1;const r=(e=>{Array.isArray(e)&&0!==e.length||(e=[["9.7",10],["9.6",10],["9.5",1]]),e.sort(((e,t)=>parseFloat(t[0])-parseFloat(e[0])));for(let[t,r]of e)if((0,p.isWcVersion)(t,">="))return r=parseInt(r,10),isNaN(r)?12:r/100*120;return 12})(t);return e<=r})(y,b))),[c,s,i,y,b,e]);return{loading:c,isDismissed:"yes"===g,dismiss:(e="analytics-overview")=>{o({[h]:"yes"}),(0,l.recordEvent)("order_attribution_install_banner_dismissed",{path:(0,u.getPath)(),context:e})},shouldShowBanner:_()}})({isInstalling:M}),{installAndActivatePlugins:N}=(0,n.useDispatch)(d.pluginsStore),E=()=>{S(!0),(0,l.recordEvent)("order_attribution_install_banner_clicked",{path:(0,u.getPath)(),context:r}),N(["woocommerce-analytics"]).then((e=>{window.location.href="admin.php?page=wc-admin&path=/analytics/order-attribution",(0,y.R)(e)})).catch((e=>{(0,y.R)(e),S(!1)}))},I=(0,a.useCallback)((()=>t===g?k&&A:v?k&&!A:k),[t,k,A,v])();if((0,a.useEffect)((()=>{I&&(0,l.recordEvent)("order_attribution_install_banner_viewed",{path:(0,u.getPath)(),context:r})}),[r,I]),!I)return null;if(t===g)return(0,w.jsx)(o.Button,{className:"woocommerce-order-attribution-install-header-banner",variant:"secondary",icon:s.A,size:"default",onClick:E,isBusy:M,disabled:M,children:(0,i.__)("Try Order Attribution","woocommerce")});const T="order-attribution-install-banner-small"===t;return(0,w.jsx)(o.Card,{size:"medium",className:"woocommerce-order-attribution-install-banner "+(T?"small":""),children:(0,w.jsxs)(o.CardBody,{className:"woocommerce-order-attribution-install-banner__body "+(T?"small":""),children:[(0,w.jsx)("div",{className:"woocommerce-order-attribution-install-banner__image_container",children:e}),(0,w.jsxs)("div",{className:"woocommerce-order-attribution-install-banner__text_container "+(T?"small":""),children:[b&&(0,w.jsx)("div",{className:"woocommerce-order-attribution-install-banner__text-badge",children:(0,w.jsx)(c.Text,{className:"woocommerce-order-attribution-install-banner__text-description",as:"p",size:"12",align:"center",children:b})}),_&&(0,w.jsx)(c.Text,{className:"woocommerce-order-attribution-install-banner__text-title",as:"p",size:"16",children:_}),x&&(0,w.jsx)(c.Text,{className:"woocommerce-order-attribution-install-banner__text-description",as:"p",size:"12",children:x}),(0,w.jsxs)("div",{children:[(0,w.jsx)(o.Button,{className:T?"small":"",variant:T?"secondary":"primary",onClick:E,iconPosition:T?"right":null,isBusy:M,disabled:M,children:j}),v&&(0,w.jsx)(o.Button,{variant:"tertiary",onClick:()=>C(r),disabled:M,children:(0,i.__)("Dismiss","woocommerce")})]})]})]})})},b=()=>(0,w.jsxs)("svg",{width:"156",height:"159",viewBox:"0 0 156 159",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,w.jsx)("rect",{width:"156",height:"159",fill:"white"}),(0,w.jsx)("rect",{width:"156",height:"156",fill:"white"}),(0,w.jsxs)("g",{clipPath:"url(#clip0_3975_26572)",children:[(0,w.jsx)("rect",{width:"142",height:"142",transform:"translate(7 7)",fill:"white"}),(0,w.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M20.0991 114.984C4.18584 87.4212 6.33794 56.3867 24.906 45.6664C34.5846 40.0785 46.813 41.1129 58.557 47.2877C58.4049 35.8334 62.3217 26.3004 70.1893 21.7581C79.375 16.4547 91.7594 19.2167 103.018 27.7644C101.3 19.4633 103.235 11.9843 108.645 8.86056C116.336 4.42016 128.137 10.4618 135.004 22.3549C141.87 34.248 141.202 47.4889 133.511 51.9293C130.493 53.6717 126.842 53.8001 123.105 52.5943C134.129 74.6404 132.449 97.8323 118.695 105.773C112.642 109.268 105.2 109.26 97.606 106.402C99.5298 123.573 94.3634 138.649 82.5332 145.48C63.9651 156.2 36.0124 142.546 20.0991 114.984Z",fill:"#F2EDFF"}),(0,w.jsx)("path",{d:"M128.714 49.074C131.36 49.074 133.785 49.7708 136 50.7838V41.1364C136 35.1926 132.613 31.8589 126.634 31.8589H29.3611C23.3874 31.8535 20 35.1872 20 41.1311V99.2724H65.7462C63.4879 102.638 61.3157 106.62 61.3157 111.283C61.3157 120.604 68.6067 128.161 77.957 128.161C87.3073 128.161 94.5983 120.604 94.5983 111.283C94.5983 106.62 92.4261 102.638 90.1678 99.2724H136V80.3582C133.785 81.3712 131.36 82.0679 128.714 82.0679C119.412 82.0679 111.869 74.8432 111.869 65.5656C111.869 56.2881 119.412 49.0633 128.714 49.0633V49.074Z",fill:"#B999FF"}),(0,w.jsx)("path",{d:"M68.268 75.1548H29.5547V79.9785H68.268V75.1548Z",fill:"#6108CE"}),(0,w.jsx)("path",{d:"M53.7505 84.8013H29.5547V89.6249H53.7505V84.8013Z",fill:"#6108CE"}),(0,w.jsx)("path",{d:"M134.864 107.13C132.799 105.495 112.05 89.6304 112.05 89.6304C112.05 89.6304 109.388 115.56 109.162 118.181C108.937 120.802 111.485 121.954 113.539 120.223C116.394 117.827 122.207 113.111 122.207 113.111C122.207 113.111 129.637 112.13 133.342 111.707C136.014 111.401 136.923 108.764 134.853 107.13H134.864Z",fill:"#5007AA"}),(0,w.jsx)("path",{d:"M68.268 41.3882H29.5547V70.3302H68.268V41.3882Z",fill:"#6108CE"}),(0,w.jsx)("path",{d:"M29.5547 70.3302C45.242 58.6068 58.3995 48.9756 68.268 41.3882V70.3302H29.5547Z",fill:"#3C087E"})]}),(0,w.jsx)("defs",{children:(0,w.jsx)("clipPath",{id:"clip0_3975_26572",children:(0,w.jsx)("rect",{width:"142",height:"142",fill:"white",transform:"translate(7 7)"})})})]})},55177:(e,t,r)=>{"use strict";r.d(t,{h:()=>n});var o=r(39793);const n=()=>(0,o.jsx)("svg",{width:"16",height:"17",viewBox:"0 0 16 17",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.68822 12.625L1.5 13.8145L1.5 1.5L14.5 1.5L14.5 12.625L2.68822 12.625ZM3.31 14.125L15 14.125C15.5523 14.125 16 13.6773 16 13.125L16 1C16 0.447717 15.5523 0 15 0H1C0.447717 0 0 0.447716 0 1V15.5247C0 15.8173 0.161234 16.086 0.419354 16.2237C0.727111 16.3878 1.10601 16.3313 1.35252 16.0845L3.31 14.125ZM12 5.99997H4V4.49997H12V5.99997ZM4 9.99997H9V8.49997H4V9.99997Z",fill:"currentColor"})})},11846:(e,t,r)=>{"use strict";r.d(t,{O:()=>y});var o=r(27723),n=r(47143),a=r(40314),s=r(56427),i=r(98846),c=r(15703),l=r(47804),u=r(36849),d=r(86087),p=r(96476),m=r(83306),h=r(39793);const f="woocommerce_task_list_reminder_bar_hidden",g=({remainingCount:e,tracksProps:t})=>{const r=1===e?(0,o.__)("🎉 Almost there. Only {{strongText}}%1$d step left{{/strongText}} get your store up and running. {{setupLink}}Finish setup{{/setupLink}}","woocommerce"):(0,o.__)("🚀 You’re doing great! {{strongText}}%1$d steps left{{/strongText}} to get your store up and running. {{setupLink}}Continue setup{{/setupLink}}","woocommerce");return(0,h.jsx)("p",{children:(0,u.A)({mixedString:(0,o.sprintf)(r,e),components:{strongText:(0,h.jsx)("strong",{}),setupLink:(0,h.jsx)(i.Link,{href:(0,c.getAdminLink)("admin.php?page=wc-admin"),onClick:()=>(0,m.recordEvent)("tasklist_reminder_bar_continue",t),type:"wp-admin",children:(0,h.jsx)(h.Fragment,{})})}})})},y=({taskListId:e,updateBodyMargin:t})=>{const{updateOptions:r}=(0,n.useDispatch)(a.optionsStore),{remainingCount:o,loading:i,taskListHidden:c,taskListComplete:u,reminderBarHidden:y,completedTasksCount:w}=(0,n.useSelect)((t=>{const{getTaskList:r,hasFinishedResolution:o}=t(a.onboardingStore),{getOption:n,hasFinishedResolution:s}=t(a.optionsStore),i=n(f),c=r(e),l=o("getTaskList",[e]),u=s("getOption",[f]),d=(0,a.getVisibleTasks)(c?.tasks||[]),p=d.filter((e=>e.isComplete))||[],m=l&&u;return{reminderBarHidden:"yes"===i,taskListHidden:!!m&&c?.isHidden,taskListComplete:!!m&&c?.isComplete,loading:!m,completedTasksCount:p.length,remainingCount:m?d?.length-p.length:null}}),[e]),v=(0,p.getQuery)(),b=v.page&&"wc-admin"===v.page&&!v.path,_=Boolean(v.wc_onboarding_active_task),x=i||c||u||y||0===w||b||_;(0,d.useEffect)((()=>{t()}),[x,t]);const j={completed:w,is_homescreen:!!b,is_active_task_page:_};return(0,d.useEffect)((()=>{i||x||(0,m.recordEvent)("tasklist_reminder_bar_view",j)}),[x,i]),x?null:(0,h.jsxs)("div",{className:"woocommerce-layout__header-tasks-reminder-bar",children:[(0,h.jsx)(g,{remainingCount:o,tracksProps:j}),(0,h.jsx)(s.Button,{isSmall:!0,onClick:()=>{r({[f]:"yes"}),(0,m.recordEvent)("tasklist_reminder_bar_close",j)},icon:l.A})]})}},56109:(e,t,r)=>{"use strict";r.d(t,{GZ:()=>p,Qk:()=>l,kY:()=>u,ne:()=>d,wm:()=>m});var o=r(27723),n=r(15703);r(24060);const a=["wcAdminSettings","preloadSettings"],s=(0,n.getSetting)("admin",{}),i=Object.keys(s).reduce(((e,t)=>(a.includes(t)||(e[t]=s[t]),e)),{}),c={onboarding:{profile:"Deprecated: wcSettings.admin.onboarding.profile is deprecated. It is planned to be released in WooCommerce 10.0.0. Please use `getProfileItems` from the onboarding store. See https://github.com/woocommerce/woocommerce/tree/trunk/packages/js/data/src/onboarding for more information.",euCountries:"Deprecated: wcSettings.admin.onboarding.euCountries is deprecated. Please use `/wc/v3/data/continents/eu` from the REST API. See https://woocommerce.github.io/woocommerce-rest-api-docs/#list-all-continents for more information.",localInfo:'Deprecated: wcSettings.admin.onboarding.localInfo is deprecated. Please use `include WC()->plugin_path() . "/i18n/locale-info.php"` instead.',currencySymbols:'"Deprecated: wcSettings.admin.onboarding.currencySymbols is deprecated. Please use get_woocommerce_currency_symbols() function instead.'}};function l(e,t=!1,r=e=>e,n=c){if(a.includes(e))throw new Error((0,o.__)("Mutable settings should be accessed via data store.","woocommerce"));return r(i.hasOwnProperty(e)?i[e]:t,t)}const u=(0,n.getSetting)("adminUrl"),d=((0,n.getSetting)("countries"),(0,n.getSetting)("currency"),(0,n.getSetting)("locale")),p=((0,n.getSetting)("siteTitle"),(0,n.getSetting)("wcAssetUrl")),m=l("orderStatuses")},51684:(e,t,r)=>{"use strict";r.d(t,{isFeatureEnabled:()=>a});var o=r(56109);const n="features";function a(e){const t=function(e){const t=(0,o.Qk)(n);return t&&t[e]}(e);return Boolean(t?.is_enabled)}},24060:(e,t,r)=>{"use strict";r.d(t,{CZ:()=>n.C,D8:()=>c,al:()=>a,s9:()=>s,vK:()=>n.v,xG:()=>l});var o=r(86087),n=r(30642);function a(e){return e?e.substr(1).split("&").reduce(((e,t)=>{const r=t.split("="),o=r[0];let n=decodeURIComponent(r[1]);return n=isNaN(Number(n))?n:Number(n),e[o]=n,e}),{}):{}}function s(){let e="";const{page:t,path:r,post_type:o}=a(window.location.search);if(t){const o="wc-admin"===t?"home_screen":t;e=r?r.replace(/\//g,"_").substring(1):o}else o&&(e=o);return e}const i=[{name:"0-2s",max:2},{name:"2-5s",max:5},{name:"5-10s",max:10},{name:"10-15s",max:15},{name:"15-20s",max:20},{name:"20-30s",max:30},{name:"30-60s",max:60},{name:">60s"}],c=e=>{for(const t of i){if(!t.max)return t.name;if(e<1e3*t.max)return t.name}},l=e=>{(0,o.useEffect)((()=>{const t=document.documentElement.classList.contains("wp-toolbar");return document.body.classList.remove("woocommerce-admin-is-loading"),document.body.classList.add(e),document.body.classList.add("woocommerce-admin-full-screen"),document.body.classList.add("is-wp-toolbar-disabled"),t&&document.documentElement.classList.remove("wp-toolbar"),()=>{document.body.classList.remove(e),document.body.classList.remove("woocommerce-admin-full-screen"),document.body.classList.remove("is-wp-toolbar-disabled"),t&&document.documentElement.classList.add("wp-toolbar")}}))}},30642:(e,t,r)=>{"use strict";function o(e){return(e||"").split(":",1)[0]}function n(e){const t=o(e);return/^woocommerce(-|_)payments$/.test(t)?"wcpay":`${t.replace(/-/g,"_")}`.split(":",1)[0]}r.d(t,{C:()=>n,v:()=>o})},89677:(e,t,r)=>{"use strict";r.d(t,{D:()=>o}),window.localStorage.getItem("xstate_inspect"),window.localStorage.getItem("xstateV5_inspect");const o=e=>({versionEnabled:undefined,xstateV5Inspector:undefined})},40368:(e,t,r)=>{"use strict";var o=r(40885),n=r(11548),a=n(o("String.prototype.indexOf"));e.exports=function(e,t){var r=o(e,!!t);return"function"==typeof r&&a(e,".prototype.")>-1?n(r):r}},11548:(e,t,r)=>{"use strict";var o=r(72418),n=r(40885),a=r(73745),s=n("%TypeError%"),i=n("%Function.prototype.apply%"),c=n("%Function.prototype.call%"),l=n("%Reflect.apply%",!0)||o.call(c,i),u=n("%Object.defineProperty%",!0),d=n("%Math.max%");if(u)try{u({},"a",{value:1})}catch(e){u=null}e.exports=function(e){if("function"!=typeof e)throw new s("a function is required");var t=l(o,c,arguments);return a(t,1+d(0,e.length-(arguments.length-1)),!0)};var p=function(){return l(o,i,arguments)};u?u(e.exports,"apply",{value:p}):e.exports.apply=p},91244:(e,t,r)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;t.splice(1,0,r,"color: inherit");let o=0,n=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(o++,"%c"===e&&(n=o))})),t.splice(n,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){return!("undefined"==typeof window||!window.process||"renderer"!==window.process.type&&!window.process.__nwjs)||("undefined"==typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r(44099)(t);const{formatters:o}=e.exports;o.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},44099:(e,t,r)=>{e.exports=function(e){function t(e){let r,n,a,s=null;function i(...e){if(!i.enabled)return;const o=i,n=Number(new Date),a=n-(r||n);o.diff=a,o.prev=r,o.curr=n,r=n,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let s=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((r,n)=>{if("%%"===r)return"%";s++;const a=t.formatters[n];if("function"==typeof a){const t=e[s];r=a.call(o,t),e.splice(s,1),s--}return r})),t.formatArgs.call(o,e),(o.log||t.log).apply(o,e)}return i.namespace=e,i.useColors=t.useColors(),i.color=t.selectColor(e),i.extend=o,i.destroy=t.destroy,Object.defineProperty(i,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(n!==t.namespaces&&(n=t.namespaces,a=t.enabled(e)),a),set:e=>{s=e}}),"function"==typeof t.init&&t.init(i),i}function o(e,r){const o=t(this.namespace+(void 0===r?":":r)+e);return o.log=this.log,o}function n(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){const e=[...t.names.map(n),...t.skips.map(n).map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){let r;t.save(e),t.namespaces=e,t.names=[],t.skips=[];const o=("string"==typeof e?e:"").split(/[\s,]+/),n=o.length;for(r=0;r<n;r++)o[r]&&("-"===(e=o[r].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.slice(1)+"$")):t.names.push(new RegExp("^"+e+"$")))},t.enabled=function(e){if("*"===e[e.length-1])return!0;let r,o;for(r=0,o=t.skips.length;r<o;r++)if(t.skips[r].test(e))return!1;for(r=0,o=t.names.length;r<o;r++)if(t.names[r].test(e))return!0;return!1},t.humanize=r(59295),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((r=>{t[r]=e[r]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r|=0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},91555:(e,t,r)=>{"use strict";var o=r(87612)(),n=r(40885),a=o&&n("%Object.defineProperty%",!0);if(a)try{a({},"a",{value:1})}catch(e){a=!1}var s=n("%SyntaxError%"),i=n("%TypeError%"),c=r(8632);e.exports=function(e,t,r){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new i("`obj` must be an object or a function`");if("string"!=typeof t&&"symbol"!=typeof t)throw new i("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!=typeof arguments[3]&&null!==arguments[3])throw new i("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!=typeof arguments[4]&&null!==arguments[4])throw new i("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!=typeof arguments[5]&&null!==arguments[5])throw new i("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!=typeof arguments[6])throw new i("`loose`, if provided, must be a boolean");var o=arguments.length>3?arguments[3]:null,n=arguments.length>4?arguments[4]:null,l=arguments.length>5?arguments[5]:null,u=arguments.length>6&&arguments[6],d=!!c&&c(e,t);if(a)a(e,t,{configurable:null===l&&d?d.configurable:!l,enumerable:null===o&&d?d.enumerable:!o,value:r,writable:null===n&&d?d.writable:!n});else{if(!u&&(o||n||l))throw new s("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");e[t]=r}}},65786:e=>{"use strict";var t=Object.prototype.toString,r=Math.max,o=function(e,t){for(var r=[],o=0;o<e.length;o+=1)r[o]=e[o];for(var n=0;n<t.length;n+=1)r[n+e.length]=t[n];return r};e.exports=function(e){var n=this;if("function"!=typeof n||"[object Function]"!==t.apply(n))throw new TypeError("Function.prototype.bind called on incompatible "+n);for(var a,s=function(e){for(var t=[],r=1,o=0;r<e.length;r+=1,o+=1)t[o]=e[r];return t}(arguments),i=r(0,n.length-s.length),c=[],l=0;l<i;l++)c[l]="$"+l;if(a=Function("binder","return function ("+function(e){for(var t="",r=0;r<e.length;r+=1)t+=e[r],r+1<e.length&&(t+=",");return t}(c)+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof a){var t=n.apply(this,o(s,arguments));return Object(t)===t?t:this}return n.apply(e,o(s,arguments))})),n.prototype){var u=function(){};u.prototype=n.prototype,a.prototype=new u,u.prototype=null}return a}},72418:(e,t,r)=>{"use strict";var o=r(65786);e.exports=Function.prototype.bind||o},40885:(e,t,r)=>{"use strict";var o,n=SyntaxError,a=Function,s=TypeError,i=function(e){try{return a('"use strict"; return ('+e+").constructor;")()}catch(e){}},c=Object.getOwnPropertyDescriptor;if(c)try{c({},"")}catch(e){c=null}var l=function(){throw new s},u=c?function(){try{return l}catch(e){try{return c(arguments,"callee").get}catch(e){return l}}}():l,d=r(13518)(),p=r(64310)(),m=Object.getPrototypeOf||(p?function(e){return e.__proto__}:null),h={},f="undefined"!=typeof Uint8Array&&m?m(Uint8Array):o,g={"%AggregateError%":"undefined"==typeof AggregateError?o:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?o:ArrayBuffer,"%ArrayIteratorPrototype%":d&&m?m([][Symbol.iterator]()):o,"%AsyncFromSyncIteratorPrototype%":o,"%AsyncFunction%":h,"%AsyncGenerator%":h,"%AsyncGeneratorFunction%":h,"%AsyncIteratorPrototype%":h,"%Atomics%":"undefined"==typeof Atomics?o:Atomics,"%BigInt%":"undefined"==typeof BigInt?o:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?o:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?o:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?o:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?o:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?o:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?o:FinalizationRegistry,"%Function%":a,"%GeneratorFunction%":h,"%Int8Array%":"undefined"==typeof Int8Array?o:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?o:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?o:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":d&&m?m(m([][Symbol.iterator]())):o,"%JSON%":"object"==typeof JSON?JSON:o,"%Map%":"undefined"==typeof Map?o:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&d&&m?m((new Map)[Symbol.iterator]()):o,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?o:Promise,"%Proxy%":"undefined"==typeof Proxy?o:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?o:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?o:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&d&&m?m((new Set)[Symbol.iterator]()):o,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?o:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":d&&m?m(""[Symbol.iterator]()):o,"%Symbol%":d?Symbol:o,"%SyntaxError%":n,"%ThrowTypeError%":u,"%TypedArray%":f,"%TypeError%":s,"%Uint8Array%":"undefined"==typeof Uint8Array?o:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?o:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?o:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?o:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?o:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?o:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?o:WeakSet};if(m)try{null.error}catch(e){var y=m(m(e));g["%Error.prototype%"]=y}var w=function e(t){var r;if("%AsyncFunction%"===t)r=i("async function () {}");else if("%GeneratorFunction%"===t)r=i("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=i("async function* () {}");else if("%AsyncGenerator%"===t){var o=e("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===t){var n=e("%AsyncGenerator%");n&&m&&(r=m(n.prototype))}return g[t]=r,r},v={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},b=r(72418),_=r(63206),x=b.call(Function.call,Array.prototype.concat),j=b.call(Function.apply,Array.prototype.splice),M=b.call(Function.call,String.prototype.replace),S=b.call(Function.call,String.prototype.slice),A=b.call(Function.call,RegExp.prototype.exec),C=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,k=/\\(\\)?/g,N=function(e,t){var r,o=e;if(_(v,o)&&(o="%"+(r=v[o])[0]+"%"),_(g,o)){var a=g[o];if(a===h&&(a=w(o)),void 0===a&&!t)throw new s("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:a}}throw new n("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new s("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new s('"allowMissing" argument must be a boolean');if(null===A(/^%?[^%]*%?$/,e))throw new n("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(e){var t=S(e,0,1),r=S(e,-1);if("%"===t&&"%"!==r)throw new n("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new n("invalid intrinsic syntax, expected opening `%`");var o=[];return M(e,C,(function(e,t,r,n){o[o.length]=r?M(n,k,"$1"):t||e})),o}(e),o=r.length>0?r[0]:"",a=N("%"+o+"%",t),i=a.name,l=a.value,u=!1,d=a.alias;d&&(o=d[0],j(r,x([0,1],d)));for(var p=1,m=!0;p<r.length;p+=1){var h=r[p],f=S(h,0,1),y=S(h,-1);if(('"'===f||"'"===f||"`"===f||'"'===y||"'"===y||"`"===y)&&f!==y)throw new n("property names with quotes must have matching quotes");if("constructor"!==h&&m||(u=!0),_(g,i="%"+(o+="."+h)+"%"))l=g[i];else if(null!=l){if(!(h in l)){if(!t)throw new s("base intrinsic for "+e+" exists, but the property is not available.");return}if(c&&p+1>=r.length){var w=c(l,h);l=(m=!!w)&&"get"in w&&!("originalValue"in w.get)?w.get:l[h]}else m=_(l,h),l=l[h];m&&!u&&(g[i]=l)}}return l}},8632:(e,t,r)=>{"use strict";var o=r(40885)("%Object.getOwnPropertyDescriptor%",!0);if(o)try{o([],"length")}catch(e){o=null}e.exports=o},30155:e=>{e.exports&&(e.exports=function(){var e=3,t=4,r=12,o=13,n=16,a=17;function s(e,t){void 0===t&&(t=0);var r=e.charCodeAt(t);if(55296<=r&&r<=56319&&t<e.length-1){var o=r;return 56320<=(n=e.charCodeAt(t+1))&&n<=57343?1024*(o-55296)+(n-56320)+65536:o}if(56320<=r&&r<=57343&&t>=1){var n=r;return 55296<=(o=e.charCodeAt(t-1))&&o<=56319?1024*(o-55296)+(n-56320)+65536:n}return r}function i(s,i,c){var l=[s].concat(i).concat([c]),u=l[l.length-2],d=c,p=l.lastIndexOf(14);if(p>1&&l.slice(1,p).every((function(t){return t==e}))&&-1==[e,o,a].indexOf(s))return 2;var m=l.lastIndexOf(t);if(m>0&&l.slice(1,m).every((function(e){return e==t}))&&-1==[r,t].indexOf(u))return l.filter((function(e){return e==t})).length%2==1?3:4;if(0==u&&1==d)return 0;if(2==u||0==u||1==u)return 14==d&&i.every((function(t){return t==e}))?2:1;if(2==d||0==d||1==d)return 1;if(6==u&&(6==d||7==d||9==d||10==d))return 0;if(!(9!=u&&7!=u||7!=d&&8!=d))return 0;if((10==u||8==u)&&8==d)return 0;if(d==e||15==d)return 0;if(5==d)return 0;if(u==r)return 0;var h=-1!=l.indexOf(e)?l.lastIndexOf(e)-1:l.length-2;return-1!=[o,a].indexOf(l[h])&&l.slice(h+1,-1).every((function(t){return t==e}))&&14==d||15==u&&-1!=[n,a].indexOf(d)?0:-1!=i.indexOf(t)?2:u==t&&d==t?0:1}function c(s){return 1536<=s&&s<=1541||1757==s||1807==s||2274==s||3406==s||69821==s||70082<=s&&s<=70083||72250==s||72326<=s&&s<=72329||73030==s?r:13==s?0:10==s?1:0<=s&&s<=9||11<=s&&s<=12||14<=s&&s<=31||127<=s&&s<=159||173==s||1564==s||6158==s||8203==s||8206<=s&&s<=8207||8232==s||8233==s||8234<=s&&s<=8238||8288<=s&&s<=8292||8293==s||8294<=s&&s<=8303||55296<=s&&s<=57343||65279==s||65520<=s&&s<=65528||65529<=s&&s<=65531||113824<=s&&s<=113827||119155<=s&&s<=119162||917504==s||917505==s||917506<=s&&s<=917535||917632<=s&&s<=917759||918e3<=s&&s<=921599?2:768<=s&&s<=879||1155<=s&&s<=1159||1160<=s&&s<=1161||1425<=s&&s<=1469||1471==s||1473<=s&&s<=1474||1476<=s&&s<=1477||1479==s||1552<=s&&s<=1562||1611<=s&&s<=1631||1648==s||1750<=s&&s<=1756||1759<=s&&s<=1764||1767<=s&&s<=1768||1770<=s&&s<=1773||1809==s||1840<=s&&s<=1866||1958<=s&&s<=1968||2027<=s&&s<=2035||2070<=s&&s<=2073||2075<=s&&s<=2083||2085<=s&&s<=2087||2089<=s&&s<=2093||2137<=s&&s<=2139||2260<=s&&s<=2273||2275<=s&&s<=2306||2362==s||2364==s||2369<=s&&s<=2376||2381==s||2385<=s&&s<=2391||2402<=s&&s<=2403||2433==s||2492==s||2494==s||2497<=s&&s<=2500||2509==s||2519==s||2530<=s&&s<=2531||2561<=s&&s<=2562||2620==s||2625<=s&&s<=2626||2631<=s&&s<=2632||2635<=s&&s<=2637||2641==s||2672<=s&&s<=2673||2677==s||2689<=s&&s<=2690||2748==s||2753<=s&&s<=2757||2759<=s&&s<=2760||2765==s||2786<=s&&s<=2787||2810<=s&&s<=2815||2817==s||2876==s||2878==s||2879==s||2881<=s&&s<=2884||2893==s||2902==s||2903==s||2914<=s&&s<=2915||2946==s||3006==s||3008==s||3021==s||3031==s||3072==s||3134<=s&&s<=3136||3142<=s&&s<=3144||3146<=s&&s<=3149||3157<=s&&s<=3158||3170<=s&&s<=3171||3201==s||3260==s||3263==s||3266==s||3270==s||3276<=s&&s<=3277||3285<=s&&s<=3286||3298<=s&&s<=3299||3328<=s&&s<=3329||3387<=s&&s<=3388||3390==s||3393<=s&&s<=3396||3405==s||3415==s||3426<=s&&s<=3427||3530==s||3535==s||3538<=s&&s<=3540||3542==s||3551==s||3633==s||3636<=s&&s<=3642||3655<=s&&s<=3662||3761==s||3764<=s&&s<=3769||3771<=s&&s<=3772||3784<=s&&s<=3789||3864<=s&&s<=3865||3893==s||3895==s||3897==s||3953<=s&&s<=3966||3968<=s&&s<=3972||3974<=s&&s<=3975||3981<=s&&s<=3991||3993<=s&&s<=4028||4038==s||4141<=s&&s<=4144||4146<=s&&s<=4151||4153<=s&&s<=4154||4157<=s&&s<=4158||4184<=s&&s<=4185||4190<=s&&s<=4192||4209<=s&&s<=4212||4226==s||4229<=s&&s<=4230||4237==s||4253==s||4957<=s&&s<=4959||5906<=s&&s<=5908||5938<=s&&s<=5940||5970<=s&&s<=5971||6002<=s&&s<=6003||6068<=s&&s<=6069||6071<=s&&s<=6077||6086==s||6089<=s&&s<=6099||6109==s||6155<=s&&s<=6157||6277<=s&&s<=6278||6313==s||6432<=s&&s<=6434||6439<=s&&s<=6440||6450==s||6457<=s&&s<=6459||6679<=s&&s<=6680||6683==s||6742==s||6744<=s&&s<=6750||6752==s||6754==s||6757<=s&&s<=6764||6771<=s&&s<=6780||6783==s||6832<=s&&s<=6845||6846==s||6912<=s&&s<=6915||6964==s||6966<=s&&s<=6970||6972==s||6978==s||7019<=s&&s<=7027||7040<=s&&s<=7041||7074<=s&&s<=7077||7080<=s&&s<=7081||7083<=s&&s<=7085||7142==s||7144<=s&&s<=7145||7149==s||7151<=s&&s<=7153||7212<=s&&s<=7219||7222<=s&&s<=7223||7376<=s&&s<=7378||7380<=s&&s<=7392||7394<=s&&s<=7400||7405==s||7412==s||7416<=s&&s<=7417||7616<=s&&s<=7673||7675<=s&&s<=7679||8204==s||8400<=s&&s<=8412||8413<=s&&s<=8416||8417==s||8418<=s&&s<=8420||8421<=s&&s<=8432||11503<=s&&s<=11505||11647==s||11744<=s&&s<=11775||12330<=s&&s<=12333||12334<=s&&s<=12335||12441<=s&&s<=12442||42607==s||42608<=s&&s<=42610||42612<=s&&s<=42621||42654<=s&&s<=42655||42736<=s&&s<=42737||43010==s||43014==s||43019==s||43045<=s&&s<=43046||43204<=s&&s<=43205||43232<=s&&s<=43249||43302<=s&&s<=43309||43335<=s&&s<=43345||43392<=s&&s<=43394||43443==s||43446<=s&&s<=43449||43452==s||43493==s||43561<=s&&s<=43566||43569<=s&&s<=43570||43573<=s&&s<=43574||43587==s||43596==s||43644==s||43696==s||43698<=s&&s<=43700||43703<=s&&s<=43704||43710<=s&&s<=43711||43713==s||43756<=s&&s<=43757||43766==s||44005==s||44008==s||44013==s||64286==s||65024<=s&&s<=65039||65056<=s&&s<=65071||65438<=s&&s<=65439||66045==s||66272==s||66422<=s&&s<=66426||68097<=s&&s<=68099||68101<=s&&s<=68102||68108<=s&&s<=68111||68152<=s&&s<=68154||68159==s||68325<=s&&s<=68326||69633==s||69688<=s&&s<=69702||69759<=s&&s<=69761||69811<=s&&s<=69814||69817<=s&&s<=69818||69888<=s&&s<=69890||69927<=s&&s<=69931||69933<=s&&s<=69940||70003==s||70016<=s&&s<=70017||70070<=s&&s<=70078||70090<=s&&s<=70092||70191<=s&&s<=70193||70196==s||70198<=s&&s<=70199||70206==s||70367==s||70371<=s&&s<=70378||70400<=s&&s<=70401||70460==s||70462==s||70464==s||70487==s||70502<=s&&s<=70508||70512<=s&&s<=70516||70712<=s&&s<=70719||70722<=s&&s<=70724||70726==s||70832==s||70835<=s&&s<=70840||70842==s||70845==s||70847<=s&&s<=70848||70850<=s&&s<=70851||71087==s||71090<=s&&s<=71093||71100<=s&&s<=71101||71103<=s&&s<=71104||71132<=s&&s<=71133||71219<=s&&s<=71226||71229==s||71231<=s&&s<=71232||71339==s||71341==s||71344<=s&&s<=71349||71351==s||71453<=s&&s<=71455||71458<=s&&s<=71461||71463<=s&&s<=71467||72193<=s&&s<=72198||72201<=s&&s<=72202||72243<=s&&s<=72248||72251<=s&&s<=72254||72263==s||72273<=s&&s<=72278||72281<=s&&s<=72283||72330<=s&&s<=72342||72344<=s&&s<=72345||72752<=s&&s<=72758||72760<=s&&s<=72765||72767==s||72850<=s&&s<=72871||72874<=s&&s<=72880||72882<=s&&s<=72883||72885<=s&&s<=72886||73009<=s&&s<=73014||73018==s||73020<=s&&s<=73021||73023<=s&&s<=73029||73031==s||92912<=s&&s<=92916||92976<=s&&s<=92982||94095<=s&&s<=94098||113821<=s&&s<=113822||119141==s||119143<=s&&s<=119145||119150<=s&&s<=119154||119163<=s&&s<=119170||119173<=s&&s<=119179||119210<=s&&s<=119213||119362<=s&&s<=119364||121344<=s&&s<=121398||121403<=s&&s<=121452||121461==s||121476==s||121499<=s&&s<=121503||121505<=s&&s<=121519||122880<=s&&s<=122886||122888<=s&&s<=122904||122907<=s&&s<=122913||122915<=s&&s<=122916||122918<=s&&s<=122922||125136<=s&&s<=125142||125252<=s&&s<=125258||917536<=s&&s<=917631||917760<=s&&s<=917999?e:127462<=s&&s<=127487?t:2307==s||2363==s||2366<=s&&s<=2368||2377<=s&&s<=2380||2382<=s&&s<=2383||2434<=s&&s<=2435||2495<=s&&s<=2496||2503<=s&&s<=2504||2507<=s&&s<=2508||2563==s||2622<=s&&s<=2624||2691==s||2750<=s&&s<=2752||2761==s||2763<=s&&s<=2764||2818<=s&&s<=2819||2880==s||2887<=s&&s<=2888||2891<=s&&s<=2892||3007==s||3009<=s&&s<=3010||3014<=s&&s<=3016||3018<=s&&s<=3020||3073<=s&&s<=3075||3137<=s&&s<=3140||3202<=s&&s<=3203||3262==s||3264<=s&&s<=3265||3267<=s&&s<=3268||3271<=s&&s<=3272||3274<=s&&s<=3275||3330<=s&&s<=3331||3391<=s&&s<=3392||3398<=s&&s<=3400||3402<=s&&s<=3404||3458<=s&&s<=3459||3536<=s&&s<=3537||3544<=s&&s<=3550||3570<=s&&s<=3571||3635==s||3763==s||3902<=s&&s<=3903||3967==s||4145==s||4155<=s&&s<=4156||4182<=s&&s<=4183||4228==s||6070==s||6078<=s&&s<=6085||6087<=s&&s<=6088||6435<=s&&s<=6438||6441<=s&&s<=6443||6448<=s&&s<=6449||6451<=s&&s<=6456||6681<=s&&s<=6682||6741==s||6743==s||6765<=s&&s<=6770||6916==s||6965==s||6971==s||6973<=s&&s<=6977||6979<=s&&s<=6980||7042==s||7073==s||7078<=s&&s<=7079||7082==s||7143==s||7146<=s&&s<=7148||7150==s||7154<=s&&s<=7155||7204<=s&&s<=7211||7220<=s&&s<=7221||7393==s||7410<=s&&s<=7411||7415==s||43043<=s&&s<=43044||43047==s||43136<=s&&s<=43137||43188<=s&&s<=43203||43346<=s&&s<=43347||43395==s||43444<=s&&s<=43445||43450<=s&&s<=43451||43453<=s&&s<=43456||43567<=s&&s<=43568||43571<=s&&s<=43572||43597==s||43755==s||43758<=s&&s<=43759||43765==s||44003<=s&&s<=44004||44006<=s&&s<=44007||44009<=s&&s<=44010||44012==s||69632==s||69634==s||69762==s||69808<=s&&s<=69810||69815<=s&&s<=69816||69932==s||70018==s||70067<=s&&s<=70069||70079<=s&&s<=70080||70188<=s&&s<=70190||70194<=s&&s<=70195||70197==s||70368<=s&&s<=70370||70402<=s&&s<=70403||70463==s||70465<=s&&s<=70468||70471<=s&&s<=70472||70475<=s&&s<=70477||70498<=s&&s<=70499||70709<=s&&s<=70711||70720<=s&&s<=70721||70725==s||70833<=s&&s<=70834||70841==s||70843<=s&&s<=70844||70846==s||70849==s||71088<=s&&s<=71089||71096<=s&&s<=71099||71102==s||71216<=s&&s<=71218||71227<=s&&s<=71228||71230==s||71340==s||71342<=s&&s<=71343||71350==s||71456<=s&&s<=71457||71462==s||72199<=s&&s<=72200||72249==s||72279<=s&&s<=72280||72343==s||72751==s||72766==s||72873==s||72881==s||72884==s||94033<=s&&s<=94078||119142==s||119149==s?5:4352<=s&&s<=4447||43360<=s&&s<=43388?6:4448<=s&&s<=4519||55216<=s&&s<=55238?7:4520<=s&&s<=4607||55243<=s&&s<=55291?8:44032==s||44060==s||44088==s||44116==s||44144==s||44172==s||44200==s||44228==s||44256==s||44284==s||44312==s||44340==s||44368==s||44396==s||44424==s||44452==s||44480==s||44508==s||44536==s||44564==s||44592==s||44620==s||44648==s||44676==s||44704==s||44732==s||44760==s||44788==s||44816==s||44844==s||44872==s||44900==s||44928==s||44956==s||44984==s||45012==s||45040==s||45068==s||45096==s||45124==s||45152==s||45180==s||45208==s||45236==s||45264==s||45292==s||45320==s||45348==s||45376==s||45404==s||45432==s||45460==s||45488==s||45516==s||45544==s||45572==s||45600==s||45628==s||45656==s||45684==s||45712==s||45740==s||45768==s||45796==s||45824==s||45852==s||45880==s||45908==s||45936==s||45964==s||45992==s||46020==s||46048==s||46076==s||46104==s||46132==s||46160==s||46188==s||46216==s||46244==s||46272==s||46300==s||46328==s||46356==s||46384==s||46412==s||46440==s||46468==s||46496==s||46524==s||46552==s||46580==s||46608==s||46636==s||46664==s||46692==s||46720==s||46748==s||46776==s||46804==s||46832==s||46860==s||46888==s||46916==s||46944==s||46972==s||47e3==s||47028==s||47056==s||47084==s||47112==s||47140==s||47168==s||47196==s||47224==s||47252==s||47280==s||47308==s||47336==s||47364==s||47392==s||47420==s||47448==s||47476==s||47504==s||47532==s||47560==s||47588==s||47616==s||47644==s||47672==s||47700==s||47728==s||47756==s||47784==s||47812==s||47840==s||47868==s||47896==s||47924==s||47952==s||47980==s||48008==s||48036==s||48064==s||48092==s||48120==s||48148==s||48176==s||48204==s||48232==s||48260==s||48288==s||48316==s||48344==s||48372==s||48400==s||48428==s||48456==s||48484==s||48512==s||48540==s||48568==s||48596==s||48624==s||48652==s||48680==s||48708==s||48736==s||48764==s||48792==s||48820==s||48848==s||48876==s||48904==s||48932==s||48960==s||48988==s||49016==s||49044==s||49072==s||49100==s||49128==s||49156==s||49184==s||49212==s||49240==s||49268==s||49296==s||49324==s||49352==s||49380==s||49408==s||49436==s||49464==s||49492==s||49520==s||49548==s||49576==s||49604==s||49632==s||49660==s||49688==s||49716==s||49744==s||49772==s||49800==s||49828==s||49856==s||49884==s||49912==s||49940==s||49968==s||49996==s||50024==s||50052==s||50080==s||50108==s||50136==s||50164==s||50192==s||50220==s||50248==s||50276==s||50304==s||50332==s||50360==s||50388==s||50416==s||50444==s||50472==s||50500==s||50528==s||50556==s||50584==s||50612==s||50640==s||50668==s||50696==s||50724==s||50752==s||50780==s||50808==s||50836==s||50864==s||50892==s||50920==s||50948==s||50976==s||51004==s||51032==s||51060==s||51088==s||51116==s||51144==s||51172==s||51200==s||51228==s||51256==s||51284==s||51312==s||51340==s||51368==s||51396==s||51424==s||51452==s||51480==s||51508==s||51536==s||51564==s||51592==s||51620==s||51648==s||51676==s||51704==s||51732==s||51760==s||51788==s||51816==s||51844==s||51872==s||51900==s||51928==s||51956==s||51984==s||52012==s||52040==s||52068==s||52096==s||52124==s||52152==s||52180==s||52208==s||52236==s||52264==s||52292==s||52320==s||52348==s||52376==s||52404==s||52432==s||52460==s||52488==s||52516==s||52544==s||52572==s||52600==s||52628==s||52656==s||52684==s||52712==s||52740==s||52768==s||52796==s||52824==s||52852==s||52880==s||52908==s||52936==s||52964==s||52992==s||53020==s||53048==s||53076==s||53104==s||53132==s||53160==s||53188==s||53216==s||53244==s||53272==s||53300==s||53328==s||53356==s||53384==s||53412==s||53440==s||53468==s||53496==s||53524==s||53552==s||53580==s||53608==s||53636==s||53664==s||53692==s||53720==s||53748==s||53776==s||53804==s||53832==s||53860==s||53888==s||53916==s||53944==s||53972==s||54e3==s||54028==s||54056==s||54084==s||54112==s||54140==s||54168==s||54196==s||54224==s||54252==s||54280==s||54308==s||54336==s||54364==s||54392==s||54420==s||54448==s||54476==s||54504==s||54532==s||54560==s||54588==s||54616==s||54644==s||54672==s||54700==s||54728==s||54756==s||54784==s||54812==s||54840==s||54868==s||54896==s||54924==s||54952==s||54980==s||55008==s||55036==s||55064==s||55092==s||55120==s||55148==s||55176==s?9:44033<=s&&s<=44059||44061<=s&&s<=44087||44089<=s&&s<=44115||44117<=s&&s<=44143||44145<=s&&s<=44171||44173<=s&&s<=44199||44201<=s&&s<=44227||44229<=s&&s<=44255||44257<=s&&s<=44283||44285<=s&&s<=44311||44313<=s&&s<=44339||44341<=s&&s<=44367||44369<=s&&s<=44395||44397<=s&&s<=44423||44425<=s&&s<=44451||44453<=s&&s<=44479||44481<=s&&s<=44507||44509<=s&&s<=44535||44537<=s&&s<=44563||44565<=s&&s<=44591||44593<=s&&s<=44619||44621<=s&&s<=44647||44649<=s&&s<=44675||44677<=s&&s<=44703||44705<=s&&s<=44731||44733<=s&&s<=44759||44761<=s&&s<=44787||44789<=s&&s<=44815||44817<=s&&s<=44843||44845<=s&&s<=44871||44873<=s&&s<=44899||44901<=s&&s<=44927||44929<=s&&s<=44955||44957<=s&&s<=44983||44985<=s&&s<=45011||45013<=s&&s<=45039||45041<=s&&s<=45067||45069<=s&&s<=45095||45097<=s&&s<=45123||45125<=s&&s<=45151||45153<=s&&s<=45179||45181<=s&&s<=45207||45209<=s&&s<=45235||45237<=s&&s<=45263||45265<=s&&s<=45291||45293<=s&&s<=45319||45321<=s&&s<=45347||45349<=s&&s<=45375||45377<=s&&s<=45403||45405<=s&&s<=45431||45433<=s&&s<=45459||45461<=s&&s<=45487||45489<=s&&s<=45515||45517<=s&&s<=45543||45545<=s&&s<=45571||45573<=s&&s<=45599||45601<=s&&s<=45627||45629<=s&&s<=45655||45657<=s&&s<=45683||45685<=s&&s<=45711||45713<=s&&s<=45739||45741<=s&&s<=45767||45769<=s&&s<=45795||45797<=s&&s<=45823||45825<=s&&s<=45851||45853<=s&&s<=45879||45881<=s&&s<=45907||45909<=s&&s<=45935||45937<=s&&s<=45963||45965<=s&&s<=45991||45993<=s&&s<=46019||46021<=s&&s<=46047||46049<=s&&s<=46075||46077<=s&&s<=46103||46105<=s&&s<=46131||46133<=s&&s<=46159||46161<=s&&s<=46187||46189<=s&&s<=46215||46217<=s&&s<=46243||46245<=s&&s<=46271||46273<=s&&s<=46299||46301<=s&&s<=46327||46329<=s&&s<=46355||46357<=s&&s<=46383||46385<=s&&s<=46411||46413<=s&&s<=46439||46441<=s&&s<=46467||46469<=s&&s<=46495||46497<=s&&s<=46523||46525<=s&&s<=46551||46553<=s&&s<=46579||46581<=s&&s<=46607||46609<=s&&s<=46635||46637<=s&&s<=46663||46665<=s&&s<=46691||46693<=s&&s<=46719||46721<=s&&s<=46747||46749<=s&&s<=46775||46777<=s&&s<=46803||46805<=s&&s<=46831||46833<=s&&s<=46859||46861<=s&&s<=46887||46889<=s&&s<=46915||46917<=s&&s<=46943||46945<=s&&s<=46971||46973<=s&&s<=46999||47001<=s&&s<=47027||47029<=s&&s<=47055||47057<=s&&s<=47083||47085<=s&&s<=47111||47113<=s&&s<=47139||47141<=s&&s<=47167||47169<=s&&s<=47195||47197<=s&&s<=47223||47225<=s&&s<=47251||47253<=s&&s<=47279||47281<=s&&s<=47307||47309<=s&&s<=47335||47337<=s&&s<=47363||47365<=s&&s<=47391||47393<=s&&s<=47419||47421<=s&&s<=47447||47449<=s&&s<=47475||47477<=s&&s<=47503||47505<=s&&s<=47531||47533<=s&&s<=47559||47561<=s&&s<=47587||47589<=s&&s<=47615||47617<=s&&s<=47643||47645<=s&&s<=47671||47673<=s&&s<=47699||47701<=s&&s<=47727||47729<=s&&s<=47755||47757<=s&&s<=47783||47785<=s&&s<=47811||47813<=s&&s<=47839||47841<=s&&s<=47867||47869<=s&&s<=47895||47897<=s&&s<=47923||47925<=s&&s<=47951||47953<=s&&s<=47979||47981<=s&&s<=48007||48009<=s&&s<=48035||48037<=s&&s<=48063||48065<=s&&s<=48091||48093<=s&&s<=48119||48121<=s&&s<=48147||48149<=s&&s<=48175||48177<=s&&s<=48203||48205<=s&&s<=48231||48233<=s&&s<=48259||48261<=s&&s<=48287||48289<=s&&s<=48315||48317<=s&&s<=48343||48345<=s&&s<=48371||48373<=s&&s<=48399||48401<=s&&s<=48427||48429<=s&&s<=48455||48457<=s&&s<=48483||48485<=s&&s<=48511||48513<=s&&s<=48539||48541<=s&&s<=48567||48569<=s&&s<=48595||48597<=s&&s<=48623||48625<=s&&s<=48651||48653<=s&&s<=48679||48681<=s&&s<=48707||48709<=s&&s<=48735||48737<=s&&s<=48763||48765<=s&&s<=48791||48793<=s&&s<=48819||48821<=s&&s<=48847||48849<=s&&s<=48875||48877<=s&&s<=48903||48905<=s&&s<=48931||48933<=s&&s<=48959||48961<=s&&s<=48987||48989<=s&&s<=49015||49017<=s&&s<=49043||49045<=s&&s<=49071||49073<=s&&s<=49099||49101<=s&&s<=49127||49129<=s&&s<=49155||49157<=s&&s<=49183||49185<=s&&s<=49211||49213<=s&&s<=49239||49241<=s&&s<=49267||49269<=s&&s<=49295||49297<=s&&s<=49323||49325<=s&&s<=49351||49353<=s&&s<=49379||49381<=s&&s<=49407||49409<=s&&s<=49435||49437<=s&&s<=49463||49465<=s&&s<=49491||49493<=s&&s<=49519||49521<=s&&s<=49547||49549<=s&&s<=49575||49577<=s&&s<=49603||49605<=s&&s<=49631||49633<=s&&s<=49659||49661<=s&&s<=49687||49689<=s&&s<=49715||49717<=s&&s<=49743||49745<=s&&s<=49771||49773<=s&&s<=49799||49801<=s&&s<=49827||49829<=s&&s<=49855||49857<=s&&s<=49883||49885<=s&&s<=49911||49913<=s&&s<=49939||49941<=s&&s<=49967||49969<=s&&s<=49995||49997<=s&&s<=50023||50025<=s&&s<=50051||50053<=s&&s<=50079||50081<=s&&s<=50107||50109<=s&&s<=50135||50137<=s&&s<=50163||50165<=s&&s<=50191||50193<=s&&s<=50219||50221<=s&&s<=50247||50249<=s&&s<=50275||50277<=s&&s<=50303||50305<=s&&s<=50331||50333<=s&&s<=50359||50361<=s&&s<=50387||50389<=s&&s<=50415||50417<=s&&s<=50443||50445<=s&&s<=50471||50473<=s&&s<=50499||50501<=s&&s<=50527||50529<=s&&s<=50555||50557<=s&&s<=50583||50585<=s&&s<=50611||50613<=s&&s<=50639||50641<=s&&s<=50667||50669<=s&&s<=50695||50697<=s&&s<=50723||50725<=s&&s<=50751||50753<=s&&s<=50779||50781<=s&&s<=50807||50809<=s&&s<=50835||50837<=s&&s<=50863||50865<=s&&s<=50891||50893<=s&&s<=50919||50921<=s&&s<=50947||50949<=s&&s<=50975||50977<=s&&s<=51003||51005<=s&&s<=51031||51033<=s&&s<=51059||51061<=s&&s<=51087||51089<=s&&s<=51115||51117<=s&&s<=51143||51145<=s&&s<=51171||51173<=s&&s<=51199||51201<=s&&s<=51227||51229<=s&&s<=51255||51257<=s&&s<=51283||51285<=s&&s<=51311||51313<=s&&s<=51339||51341<=s&&s<=51367||51369<=s&&s<=51395||51397<=s&&s<=51423||51425<=s&&s<=51451||51453<=s&&s<=51479||51481<=s&&s<=51507||51509<=s&&s<=51535||51537<=s&&s<=51563||51565<=s&&s<=51591||51593<=s&&s<=51619||51621<=s&&s<=51647||51649<=s&&s<=51675||51677<=s&&s<=51703||51705<=s&&s<=51731||51733<=s&&s<=51759||51761<=s&&s<=51787||51789<=s&&s<=51815||51817<=s&&s<=51843||51845<=s&&s<=51871||51873<=s&&s<=51899||51901<=s&&s<=51927||51929<=s&&s<=51955||51957<=s&&s<=51983||51985<=s&&s<=52011||52013<=s&&s<=52039||52041<=s&&s<=52067||52069<=s&&s<=52095||52097<=s&&s<=52123||52125<=s&&s<=52151||52153<=s&&s<=52179||52181<=s&&s<=52207||52209<=s&&s<=52235||52237<=s&&s<=52263||52265<=s&&s<=52291||52293<=s&&s<=52319||52321<=s&&s<=52347||52349<=s&&s<=52375||52377<=s&&s<=52403||52405<=s&&s<=52431||52433<=s&&s<=52459||52461<=s&&s<=52487||52489<=s&&s<=52515||52517<=s&&s<=52543||52545<=s&&s<=52571||52573<=s&&s<=52599||52601<=s&&s<=52627||52629<=s&&s<=52655||52657<=s&&s<=52683||52685<=s&&s<=52711||52713<=s&&s<=52739||52741<=s&&s<=52767||52769<=s&&s<=52795||52797<=s&&s<=52823||52825<=s&&s<=52851||52853<=s&&s<=52879||52881<=s&&s<=52907||52909<=s&&s<=52935||52937<=s&&s<=52963||52965<=s&&s<=52991||52993<=s&&s<=53019||53021<=s&&s<=53047||53049<=s&&s<=53075||53077<=s&&s<=53103||53105<=s&&s<=53131||53133<=s&&s<=53159||53161<=s&&s<=53187||53189<=s&&s<=53215||53217<=s&&s<=53243||53245<=s&&s<=53271||53273<=s&&s<=53299||53301<=s&&s<=53327||53329<=s&&s<=53355||53357<=s&&s<=53383||53385<=s&&s<=53411||53413<=s&&s<=53439||53441<=s&&s<=53467||53469<=s&&s<=53495||53497<=s&&s<=53523||53525<=s&&s<=53551||53553<=s&&s<=53579||53581<=s&&s<=53607||53609<=s&&s<=53635||53637<=s&&s<=53663||53665<=s&&s<=53691||53693<=s&&s<=53719||53721<=s&&s<=53747||53749<=s&&s<=53775||53777<=s&&s<=53803||53805<=s&&s<=53831||53833<=s&&s<=53859||53861<=s&&s<=53887||53889<=s&&s<=53915||53917<=s&&s<=53943||53945<=s&&s<=53971||53973<=s&&s<=53999||54001<=s&&s<=54027||54029<=s&&s<=54055||54057<=s&&s<=54083||54085<=s&&s<=54111||54113<=s&&s<=54139||54141<=s&&s<=54167||54169<=s&&s<=54195||54197<=s&&s<=54223||54225<=s&&s<=54251||54253<=s&&s<=54279||54281<=s&&s<=54307||54309<=s&&s<=54335||54337<=s&&s<=54363||54365<=s&&s<=54391||54393<=s&&s<=54419||54421<=s&&s<=54447||54449<=s&&s<=54475||54477<=s&&s<=54503||54505<=s&&s<=54531||54533<=s&&s<=54559||54561<=s&&s<=54587||54589<=s&&s<=54615||54617<=s&&s<=54643||54645<=s&&s<=54671||54673<=s&&s<=54699||54701<=s&&s<=54727||54729<=s&&s<=54755||54757<=s&&s<=54783||54785<=s&&s<=54811||54813<=s&&s<=54839||54841<=s&&s<=54867||54869<=s&&s<=54895||54897<=s&&s<=54923||54925<=s&&s<=54951||54953<=s&&s<=54979||54981<=s&&s<=55007||55009<=s&&s<=55035||55037<=s&&s<=55063||55065<=s&&s<=55091||55093<=s&&s<=55119||55121<=s&&s<=55147||55149<=s&&s<=55175||55177<=s&&s<=55203?10:9757==s||9977==s||9994<=s&&s<=9997||127877==s||127938<=s&&s<=127940||127943==s||127946<=s&&s<=127948||128066<=s&&s<=128067||128070<=s&&s<=128080||128110==s||128112<=s&&s<=128120||128124==s||128129<=s&&s<=128131||128133<=s&&s<=128135||128170==s||128372<=s&&s<=128373||128378==s||128400==s||128405<=s&&s<=128406||128581<=s&&s<=128583||128587<=s&&s<=128591||128675==s||128692<=s&&s<=128694||128704==s||128716==s||129304<=s&&s<=129308||129310<=s&&s<=129311||129318==s||129328<=s&&s<=129337||129341<=s&&s<=129342||129489<=s&&s<=129501?o:127995<=s&&s<=127999?14:8205==s?15:9792==s||9794==s||9877<=s&&s<=9878||9992==s||10084==s||127752==s||127806==s||127859==s||127891==s||127908==s||127912==s||127979==s||127981==s||128139==s||128187<=s&&s<=128188||128295==s||128300==s||128488==s||128640==s||128658==s?n:128102<=s&&s<=128105?a:11}return this.nextBreak=function(e,t){if(void 0===t&&(t=0),t<0)return 0;if(t>=e.length-1)return e.length;for(var r,o,n=c(s(e,t)),a=[],l=t+1;l<e.length;l++)if(o=l-1,!(55296<=(r=e).charCodeAt(o)&&r.charCodeAt(o)<=56319&&56320<=r.charCodeAt(o+1)&&r.charCodeAt(o+1)<=57343)){var u=c(s(e,l));if(i(n,a,u))return l;a.push(u)}return e.length},this.splitGraphemes=function(e){for(var t,r=[],o=0;(t=this.nextBreak(e,o))<e.length;)r.push(e.slice(o,t)),o=t;return o<e.length&&r.push(e.slice(o)),r},this.iterateGraphemes=function(e){var t=0,r={next:function(){var r,o;return(o=this.nextBreak(e,t))<e.length?(r=e.slice(t,o),t=o,{value:r,done:!1}):t<e.length?(r=e.slice(t),t=e.length,{value:r,done:!1}):{value:void 0,done:!0}}.bind(this)};return"undefined"!=typeof Symbol&&Symbol.iterator&&(r[Symbol.iterator]=function(){return r}),r},this.countGraphemes=function(e){for(var t,r=0,o=0;(t=this.nextBreak(e,o))<e.length;)o=t,r++;return o<e.length&&r++,r},this})},8181:(e,t,r)=>{"use strict";t.A=function(e){var t=e.size,r=void 0===t?24:t,o=e.onClick,i=(e.icon,e.className),c=function(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r,o,n={},a=Object.keys(e);for(o=0;o<a.length;o++)r=a[o],0<=t.indexOf(r)||(n[r]=e[r]);return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)r=a[o],0<=t.indexOf(r)||Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,a),l=["gridicon","gridicons-cross-small",i,!1,!1,!1].filter(Boolean).join(" ");return n.default.createElement("svg",s({className:l,height:r,width:r,onClick:o},c,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),n.default.createElement("g",null,n.default.createElement("path",{d:"M17.705 7.705l-1.41-1.41L12 10.59 7.705 6.295l-1.41 1.41L10.59 12l-4.295 4.295 1.41 1.41L12 13.41l4.295 4.295 1.41-1.41L13.41 12l4.295-4.295z"})))};var o,n=(o=r(51609))&&o.__esModule?o:{default:o},a=["size","onClick","icon","className"];function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t,r=1;r<arguments.length;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},s.apply(this,arguments)}},87612:(e,t,r)=>{"use strict";var o=r(40885)("%Object.defineProperty%",!0),n=function(){if(o)try{return o({},"a",{value:1}),!0}catch(e){return!1}return!1};n.hasArrayLengthDefineBug=function(){if(!n())return null;try{return 1!==o([],"length",{value:1}).length}catch(e){return!0}},e.exports=n},64310:e=>{"use strict";var t={foo:{}},r=Object;e.exports=function(){return{__proto__:t}.foo===t.foo&&!({__proto__:null}instanceof r)}},13518:(e,t,r)=>{"use strict";var o="undefined"!=typeof Symbol&&Symbol,n=r(71108);e.exports=function(){return"function"==typeof o&&"function"==typeof Symbol&&"symbol"==typeof o("foo")&&"symbol"==typeof Symbol("bar")&&n()}},71108:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(t in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var o=Object.getOwnPropertySymbols(e);if(1!==o.length||o[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var n=Object.getOwnPropertyDescriptor(e,t);if(42!==n.value||!0!==n.enumerable)return!1}return!0}},63206:(e,t,r)=>{"use strict";var o=Function.prototype.call,n=Object.prototype.hasOwnProperty,a=r(72418);e.exports=a.call(o,n)},59295:e=>{var t=1e3,r=60*t,o=60*r,n=24*o,a=7*n;function s(e,t,r,o){var n=t>=1.5*r;return Math.round(e/r)+" "+o+(n?"s":"")}e.exports=function(e,i){i=i||{};var c,l,u=typeof e;if("string"===u&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var s=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(s){var i=parseFloat(s[1]);switch((s[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*i;case"weeks":case"week":case"w":return i*a;case"days":case"day":case"d":return i*n;case"hours":case"hour":case"hrs":case"hr":case"h":return i*o;case"minutes":case"minute":case"mins":case"min":case"m":return i*r;case"seconds":case"second":case"secs":case"sec":case"s":return i*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return i;default:return}}}}(e);if("number"===u&&isFinite(e))return i.long?(c=e,(l=Math.abs(c))>=n?s(c,l,n,"day"):l>=o?s(c,l,o,"hour"):l>=r?s(c,l,r,"minute"):l>=t?s(c,l,t,"second"):c+" ms"):function(e){var a=Math.abs(e);return a>=n?Math.round(e/n)+"d":a>=o?Math.round(e/o)+"h":a>=r?Math.round(e/r)+"m":a>=t?Math.round(e/t)+"s":e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},83282:(e,t,r)=>{var o="function"==typeof Map&&Map.prototype,n=Object.getOwnPropertyDescriptor&&o?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,a=o&&n&&"function"==typeof n.get?n.get:null,s=o&&Map.prototype.forEach,i="function"==typeof Set&&Set.prototype,c=Object.getOwnPropertyDescriptor&&i?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,l=i&&c&&"function"==typeof c.get?c.get:null,u=i&&Set.prototype.forEach,d="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,p="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,m="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,h=Boolean.prototype.valueOf,f=Object.prototype.toString,g=Function.prototype.toString,y=String.prototype.match,w=String.prototype.slice,v=String.prototype.replace,b=String.prototype.toUpperCase,_=String.prototype.toLowerCase,x=RegExp.prototype.test,j=Array.prototype.concat,M=Array.prototype.join,S=Array.prototype.slice,A=Math.floor,C="function"==typeof BigInt?BigInt.prototype.valueOf:null,k=Object.getOwnPropertySymbols,N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,E="function"==typeof Symbol&&"object"==typeof Symbol.iterator,I="function"==typeof Symbol&&Symbol.toStringTag&&(Symbol.toStringTag,1)?Symbol.toStringTag:null,T=Object.prototype.propertyIsEnumerable,P=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function O(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||x.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var o=e<0?-A(-e):A(e);if(o!==e){var n=String(o),a=w.call(t,n.length+1);return v.call(n,r,"$&_")+"."+v.call(v.call(a,/([0-9]{3})/g,"$&_"),/_$/,"")}}return v.call(t,r,"$&_")}var D=r(70123),L=D.custom,R=B(L)?L:null;function z(e,t,r){var o="double"===(r.quoteStyle||t)?'"':"'";return o+e+o}function F(e){return v.call(String(e),/"/g,"&quot;")}function U(e){return!("[object Array]"!==G(e)||I&&"object"==typeof e&&I in e)}function V(e){return!("[object RegExp]"!==G(e)||I&&"object"==typeof e&&I in e)}function B(e){if(E)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!N)return!1;try{return N.call(e),!0}catch(e){}return!1}e.exports=function e(t,o,n,i){var c=o||{};if(H(c,"quoteStyle")&&"single"!==c.quoteStyle&&"double"!==c.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(H(c,"maxStringLength")&&("number"==typeof c.maxStringLength?c.maxStringLength<0&&c.maxStringLength!==1/0:null!==c.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var f=!H(c,"customInspect")||c.customInspect;if("boolean"!=typeof f&&"symbol"!==f)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(H(c,"indent")&&null!==c.indent&&"\t"!==c.indent&&!(parseInt(c.indent,10)===c.indent&&c.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(H(c,"numericSeparator")&&"boolean"!=typeof c.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var b=c.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return W(t,c);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var x=String(t);return b?O(t,x):x}if("bigint"==typeof t){var A=String(t)+"n";return b?O(t,A):A}var k=void 0===c.depth?5:c.depth;if(void 0===n&&(n=0),n>=k&&k>0&&"object"==typeof t)return U(t)?"[Array]":"[Object]";var L,Q=function(e,t){var r;if("\t"===e.indent)r="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;r=M.call(Array(e.indent+1)," ")}return{base:r,prev:M.call(Array(t+1),r)}}(c,n);if(void 0===i)i=[];else if(Y(i,t)>=0)return"[Circular]";function $(t,r,o){if(r&&(i=S.call(i)).push(r),o){var a={depth:c.depth};return H(c,"quoteStyle")&&(a.quoteStyle=c.quoteStyle),e(t,a,n+1,i)}return e(t,c,n+1,i)}if("function"==typeof t&&!V(t)){var ee=function(e){if(e.name)return e.name;var t=y.call(g.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}(t),te=X(t,$);return"[Function"+(ee?": "+ee:" (anonymous)")+"]"+(te.length>0?" { "+M.call(te,", ")+" }":"")}if(B(t)){var re=E?v.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):N.call(t);return"object"!=typeof t||E?re:q(re)}if((L=t)&&"object"==typeof L&&("undefined"!=typeof HTMLElement&&L instanceof HTMLElement||"string"==typeof L.nodeName&&"function"==typeof L.getAttribute)){for(var oe="<"+_.call(String(t.nodeName)),ne=t.attributes||[],ae=0;ae<ne.length;ae++)oe+=" "+ne[ae].name+"="+z(F(ne[ae].value),"double",c);return oe+=">",t.childNodes&&t.childNodes.length&&(oe+="..."),oe+"</"+_.call(String(t.nodeName))+">"}if(U(t)){if(0===t.length)return"[]";var se=X(t,$);return Q&&!function(e){for(var t=0;t<e.length;t++)if(Y(e[t],"\n")>=0)return!1;return!0}(se)?"["+K(se,Q)+"]":"[ "+M.call(se,", ")+" ]"}if(function(e){return!("[object Error]"!==G(e)||I&&"object"==typeof e&&I in e)}(t)){var ie=X(t,$);return"cause"in Error.prototype||!("cause"in t)||T.call(t,"cause")?0===ie.length?"["+String(t)+"]":"{ ["+String(t)+"] "+M.call(ie,", ")+" }":"{ ["+String(t)+"] "+M.call(j.call("[cause]: "+$(t.cause),ie),", ")+" }"}if("object"==typeof t&&f){if(R&&"function"==typeof t[R]&&D)return D(t,{depth:k-n});if("symbol"!==f&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!a||!e||"object"!=typeof e)return!1;try{a.call(e);try{l.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var ce=[];return s&&s.call(t,(function(e,r){ce.push($(r,t,!0)+" => "+$(e,t))})),J("Map",a.call(t),ce,Q)}if(function(e){if(!l||!e||"object"!=typeof e)return!1;try{l.call(e);try{a.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var le=[];return u&&u.call(t,(function(e){le.push($(e,t))})),J("Set",l.call(t),le,Q)}if(function(e){if(!d||!e||"object"!=typeof e)return!1;try{d.call(e,d);try{p.call(e,p)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return Z("WeakMap");if(function(e){if(!p||!e||"object"!=typeof e)return!1;try{p.call(e,p);try{d.call(e,d)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return Z("WeakSet");if(function(e){if(!m||!e||"object"!=typeof e)return!1;try{return m.call(e),!0}catch(e){}return!1}(t))return Z("WeakRef");if(function(e){return!("[object Number]"!==G(e)||I&&"object"==typeof e&&I in e)}(t))return q($(Number(t)));if(function(e){if(!e||"object"!=typeof e||!C)return!1;try{return C.call(e),!0}catch(e){}return!1}(t))return q($(C.call(t)));if(function(e){return!("[object Boolean]"!==G(e)||I&&"object"==typeof e&&I in e)}(t))return q(h.call(t));if(function(e){return!("[object String]"!==G(e)||I&&"object"==typeof e&&I in e)}(t))return q($(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if(t===r.g)return"{ [object globalThis] }";if(!function(e){return!("[object Date]"!==G(e)||I&&"object"==typeof e&&I in e)}(t)&&!V(t)){var ue=X(t,$),de=P?P(t)===Object.prototype:t instanceof Object||t.constructor===Object,pe=t instanceof Object?"":"null prototype",me=!de&&I&&Object(t)===t&&I in t?w.call(G(t),8,-1):pe?"Object":"",he=(de||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(me||pe?"["+M.call(j.call([],me||[],pe||[]),": ")+"] ":"");return 0===ue.length?he+"{}":Q?he+"{"+K(ue,Q)+"}":he+"{ "+M.call(ue,", ")+" }"}return String(t)};var Q=Object.prototype.hasOwnProperty||function(e){return e in this};function H(e,t){return Q.call(e,t)}function G(e){return f.call(e)}function Y(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,o=e.length;r<o;r++)if(e[r]===t)return r;return-1}function W(e,t){if(e.length>t.maxStringLength){var r=e.length-t.maxStringLength,o="... "+r+" more character"+(r>1?"s":"");return W(w.call(e,0,t.maxStringLength),t)+o}return z(v.call(v.call(e,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,$),"single",t)}function $(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+b.call(t.toString(16))}function q(e){return"Object("+e+")"}function Z(e){return e+" { ? }"}function J(e,t,r,o){return e+" ("+t+") {"+(o?K(r,o):M.call(r,", "))+"}"}function K(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+M.call(e,","+r)+"\n"+t.prev}function X(e,t){var r=U(e),o=[];if(r){o.length=e.length;for(var n=0;n<e.length;n++)o[n]=H(e,n)?t(e[n],e):""}var a,s="function"==typeof k?k(e):[];if(E){a={};for(var i=0;i<s.length;i++)a["$"+s[i]]=s[i]}for(var c in e)H(e,c)&&(r&&String(Number(c))===c&&c<e.length||E&&a["$"+c]instanceof Symbol||(x.call(/[^\w$]/,c)?o.push(t(c,e)+": "+t(e[c],e)):o.push(c+": "+t(e[c],e))));if("function"==typeof k)for(var l=0;l<s.length;l++)T.call(e,s[l])&&o.push("["+t(s[l])+"]: "+t(e[s[l]],e));return o}},24294:e=>{"use strict";var t=String.prototype.replace,r=/%20/g,o="RFC3986";e.exports={default:o,formatters:{RFC1738:function(e){return t.call(e,r,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:o}},4594:(e,t,r)=>{"use strict";var o=r(61007),n=r(84977),a=r(24294);e.exports={formats:a,parse:n,stringify:o}},84977:(e,t,r)=>{"use strict";var o=r(50323),n=Object.prototype.hasOwnProperty,a=Array.isArray,s={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:o.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},i=function(e){return e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(parseInt(t,10))}))},c=function(e,t){return e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1?e.split(","):e},l=function(e,t,r,o){if(e){var a=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,s=/(\[[^[\]]*])/g,i=r.depth>0&&/(\[[^[\]]*])/.exec(a),l=i?a.slice(0,i.index):a,u=[];if(l){if(!r.plainObjects&&n.call(Object.prototype,l)&&!r.allowPrototypes)return;u.push(l)}for(var d=0;r.depth>0&&null!==(i=s.exec(a))&&d<r.depth;){if(d+=1,!r.plainObjects&&n.call(Object.prototype,i[1].slice(1,-1))&&!r.allowPrototypes)return;u.push(i[1])}return i&&u.push("["+a.slice(i.index)+"]"),function(e,t,r,o){for(var n=o?t:c(t,r),a=e.length-1;a>=0;--a){var s,i=e[a];if("[]"===i&&r.parseArrays)s=[].concat(n);else{s=r.plainObjects?Object.create(null):{};var l="["===i.charAt(0)&&"]"===i.charAt(i.length-1)?i.slice(1,-1):i,u=parseInt(l,10);r.parseArrays||""!==l?!isNaN(u)&&i!==l&&String(u)===l&&u>=0&&r.parseArrays&&u<=r.arrayLimit?(s=[])[u]=n:"__proto__"!==l&&(s[l]=n):s={0:n}}n=s}return n}(u,t,r,o)}};e.exports=function(e,t){var r=function(e){if(!e)return s;if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var t=void 0===e.charset?s.charset:e.charset;return{allowDots:void 0===e.allowDots?s.allowDots:!!e.allowDots,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:s.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:s.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:s.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:s.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:s.comma,decoder:"function"==typeof e.decoder?e.decoder:s.decoder,delimiter:"string"==typeof e.delimiter||o.isRegExp(e.delimiter)?e.delimiter:s.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:s.depth,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:s.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:s.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:s.plainObjects,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:s.strictNullHandling}}(t);if(""===e||null==e)return r.plainObjects?Object.create(null):{};for(var u="string"==typeof e?function(e,t){var r,l={__proto__:null},u=t.ignoreQueryPrefix?e.replace(/^\?/,""):e,d=t.parameterLimit===1/0?void 0:t.parameterLimit,p=u.split(t.delimiter,d),m=-1,h=t.charset;if(t.charsetSentinel)for(r=0;r<p.length;++r)0===p[r].indexOf("utf8=")&&("utf8=%E2%9C%93"===p[r]?h="utf-8":"utf8=%26%2310003%3B"===p[r]&&(h="iso-8859-1"),m=r,r=p.length);for(r=0;r<p.length;++r)if(r!==m){var f,g,y=p[r],w=y.indexOf("]="),v=-1===w?y.indexOf("="):w+1;-1===v?(f=t.decoder(y,s.decoder,h,"key"),g=t.strictNullHandling?null:""):(f=t.decoder(y.slice(0,v),s.decoder,h,"key"),g=o.maybeMap(c(y.slice(v+1),t),(function(e){return t.decoder(e,s.decoder,h,"value")}))),g&&t.interpretNumericEntities&&"iso-8859-1"===h&&(g=i(g)),y.indexOf("[]=")>-1&&(g=a(g)?[g]:g),n.call(l,f)?l[f]=o.combine(l[f],g):l[f]=g}return l}(e,r):e,d=r.plainObjects?Object.create(null):{},p=Object.keys(u),m=0;m<p.length;++m){var h=p[m],f=l(h,u[h],r,"string"==typeof e);d=o.merge(d,f,r)}return!0===r.allowSparse?d:o.compact(d)}},61007:(e,t,r)=>{"use strict";var o=r(2435),n=r(50323),a=r(24294),s=Object.prototype.hasOwnProperty,i={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},c=Array.isArray,l=Array.prototype.push,u=function(e,t){l.apply(e,c(t)?t:[t])},d=Date.prototype.toISOString,p=a.default,m={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:n.encode,encodeValuesOnly:!1,format:p,formatter:a.formatters[p],indices:!1,serializeDate:function(e){return d.call(e)},skipNulls:!1,strictNullHandling:!1},h={},f=function e(t,r,a,s,i,l,d,p,f,g,y,w,v,b,_,x){for(var j,M=t,S=x,A=0,C=!1;void 0!==(S=S.get(h))&&!C;){var k=S.get(t);if(A+=1,void 0!==k){if(k===A)throw new RangeError("Cyclic object value");C=!0}void 0===S.get(h)&&(A=0)}if("function"==typeof p?M=p(r,M):M instanceof Date?M=y(M):"comma"===a&&c(M)&&(M=n.maybeMap(M,(function(e){return e instanceof Date?y(e):e}))),null===M){if(i)return d&&!b?d(r,m.encoder,_,"key",w):r;M=""}if("string"==typeof(j=M)||"number"==typeof j||"boolean"==typeof j||"symbol"==typeof j||"bigint"==typeof j||n.isBuffer(M))return d?[v(b?r:d(r,m.encoder,_,"key",w))+"="+v(d(M,m.encoder,_,"value",w))]:[v(r)+"="+v(String(M))];var N,E=[];if(void 0===M)return E;if("comma"===a&&c(M))b&&d&&(M=n.maybeMap(M,d)),N=[{value:M.length>0?M.join(",")||null:void 0}];else if(c(p))N=p;else{var I=Object.keys(M);N=f?I.sort(f):I}for(var T=s&&c(M)&&1===M.length?r+"[]":r,P=0;P<N.length;++P){var O=N[P],D="object"==typeof O&&void 0!==O.value?O.value:M[O];if(!l||null!==D){var L=c(M)?"function"==typeof a?a(T,O):T:T+(g?"."+O:"["+O+"]");x.set(t,A);var R=o();R.set(h,x),u(E,e(D,L,a,s,i,l,"comma"===a&&b&&c(M)?null:d,p,f,g,y,w,v,b,_,R))}}return E};e.exports=function(e,t){var r,n=e,l=function(e){if(!e)return m;if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw new TypeError("Encoder has to be a function.");var t=e.charset||m.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=a.default;if(void 0!==e.format){if(!s.call(a.formatters,e.format))throw new TypeError("Unknown format option provided.");r=e.format}var o=a.formatters[r],n=m.filter;return("function"==typeof e.filter||c(e.filter))&&(n=e.filter),{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:m.addQueryPrefix,allowDots:void 0===e.allowDots?m.allowDots:!!e.allowDots,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:m.charsetSentinel,delimiter:void 0===e.delimiter?m.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:m.encode,encoder:"function"==typeof e.encoder?e.encoder:m.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:m.encodeValuesOnly,filter:n,format:r,formatter:o,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:m.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:m.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:m.strictNullHandling}}(t);"function"==typeof l.filter?n=(0,l.filter)("",n):c(l.filter)&&(r=l.filter);var d,p=[];if("object"!=typeof n||null===n)return"";d=t&&t.arrayFormat in i?t.arrayFormat:t&&"indices"in t?t.indices?"indices":"repeat":"indices";var h=i[d];if(t&&"commaRoundTrip"in t&&"boolean"!=typeof t.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var g="comma"===h&&t&&t.commaRoundTrip;r||(r=Object.keys(n)),l.sort&&r.sort(l.sort);for(var y=o(),w=0;w<r.length;++w){var v=r[w];l.skipNulls&&null===n[v]||u(p,f(n[v],v,h,g,l.strictNullHandling,l.skipNulls,l.encode?l.encoder:null,l.filter,l.sort,l.allowDots,l.serializeDate,l.format,l.formatter,l.encodeValuesOnly,l.charset,y))}var b=p.join(l.delimiter),_=!0===l.addQueryPrefix?"?":"";return l.charsetSentinel&&("iso-8859-1"===l.charset?_+="utf8=%26%2310003%3B&":_+="utf8=%E2%9C%93&"),b.length>0?_+b:""}},50323:(e,t,r)=>{"use strict";var o=r(24294),n=Object.prototype.hasOwnProperty,a=Array.isArray,s=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),i=function(e,t){for(var r=t&&t.plainObjects?Object.create(null):{},o=0;o<e.length;++o)void 0!==e[o]&&(r[o]=e[o]);return r};e.exports={arrayToObject:i,assign:function(e,t){return Object.keys(t).reduce((function(e,r){return e[r]=t[r],e}),e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],o=0;o<t.length;++o)for(var n=t[o],s=n.obj[n.prop],i=Object.keys(s),c=0;c<i.length;++c){var l=i[c],u=s[l];"object"==typeof u&&null!==u&&-1===r.indexOf(u)&&(t.push({obj:s,prop:l}),r.push(u))}return function(e){for(;e.length>1;){var t=e.pop(),r=t.obj[t.prop];if(a(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);t.obj[t.prop]=o}}}(t),e},decode:function(e,t,r){var o=e.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(e){return o}},encode:function(e,t,r,n,a){if(0===e.length)return e;var i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===r)return escape(i).replace(/%u[0-9a-f]{4}/gi,(function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"}));for(var c="",l=0;l<i.length;++l){var u=i.charCodeAt(l);45===u||46===u||95===u||126===u||u>=48&&u<=57||u>=65&&u<=90||u>=97&&u<=122||a===o.RFC1738&&(40===u||41===u)?c+=i.charAt(l):u<128?c+=s[u]:u<2048?c+=s[192|u>>6]+s[128|63&u]:u<55296||u>=57344?c+=s[224|u>>12]+s[128|u>>6&63]+s[128|63&u]:(l+=1,u=65536+((1023&u)<<10|1023&i.charCodeAt(l)),c+=s[240|u>>18]+s[128|u>>12&63]+s[128|u>>6&63]+s[128|63&u])}return c},isBuffer:function(e){return!(!e||"object"!=typeof e||!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e)))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(a(e)){for(var r=[],o=0;o<e.length;o+=1)r.push(t(e[o]));return r}return t(e)},merge:function e(t,r,o){if(!r)return t;if("object"!=typeof r){if(a(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(o&&(o.plainObjects||o.allowPrototypes)||!n.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var s=t;return a(t)&&!a(r)&&(s=i(t,o)),a(t)&&a(r)?(r.forEach((function(r,a){if(n.call(t,a)){var s=t[a];s&&"object"==typeof s&&r&&"object"==typeof r?t[a]=e(s,r,o):t.push(r)}else t[a]=r})),t):Object.keys(r).reduce((function(t,a){var s=r[a];return n.call(t,a)?t[a]=e(t[a],s,o):t[a]=s,t}),s)}}},48808:(e,t,r)=>{"use strict";r.d(t,{ok:()=>s,rI:()=>a});var o=r(51609),n=r(33068);function a(e){let{basename:t,children:r,history:a}=e;const[s,i]=(0,o.useState)({action:a.action,location:a.location});return(0,o.useLayoutEffect)((()=>a.listen(i)),[a]),(0,o.createElement)(n.Ix,{basename:t,children:r,location:s.location,navigationType:s.action,navigator:a})}function s(e){let t=(0,o.useRef)(i(e)),r=(0,n.zy)(),a=(0,o.useMemo)((()=>{let e=i(r.search);for(let r of t.current.keys())e.has(r)||t.current.getAll(r).forEach((t=>{e.append(r,t)}));return e}),[r.search]),s=(0,n.Zp)();return[a,(0,o.useCallback)(((e,t)=>{s("?"+i(e),t)}),[s])]}function i(e){return void 0===e&&(e=""),new URLSearchParams("string"==typeof e||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce(((t,r)=>{let o=e[r];return t.concat(Array.isArray(o)?o.map((e=>[r,e])):[[r,o]])}),[]))}},33068:(e,t,r)=>{"use strict";var o;function n(e){var t={};if(e){var r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));var o=e.indexOf("?");o>=0&&(t.search=e.substr(o),e=e.substr(0,o)),e&&(t.pathname=e)}return t}r.d(t,{qh:()=>T,Ix:()=>P,BV:()=>O,zy:()=>k,RQ:()=>N,Zp:()=>E,g:()=>I}),function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(o||(o={}));var a=r(51609);const s=(0,a.createContext)(null),i=(0,a.createContext)(null),c=(0,a.createContext)({outlet:null,matches:[]});function l(e,t){if(!e)throw new Error(t)}function u(e,t,r){void 0===r&&(r="/");let o=x(("string"==typeof t?n(t):t).pathname||"/",r);if(null==o)return null;let a=d(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){return e.length===t.length&&e.slice(0,-1).every(((e,r)=>e===t[r]))?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(a);let s=null;for(let e=0;null==s&&e<a.length;++e)s=b(a[e],o);return s}function d(e,t,r,o){return void 0===t&&(t=[]),void 0===r&&(r=[]),void 0===o&&(o=""),e.forEach(((e,n)=>{let a={relativePath:e.path||"",caseSensitive:!0===e.caseSensitive,childrenIndex:n,route:e};a.relativePath.startsWith("/")&&(a.relativePath.startsWith(o)||l(!1),a.relativePath=a.relativePath.slice(o.length));let s=j([o,a.relativePath]),i=r.concat(a);e.children&&e.children.length>0&&(!0===e.index&&l(!1),d(e.children,t,i,s)),(null!=e.path||e.index)&&t.push({path:s,score:v(s,e.index),routesMeta:i})})),t}const p=/^:\w+$/,m=3,h=2,f=1,g=10,y=-2,w=e=>"*"===e;function v(e,t){let r=e.split("/"),o=r.length;return r.some(w)&&(o+=y),t&&(o+=h),r.filter((e=>!w(e))).reduce(((e,t)=>e+(p.test(t)?m:""===t?f:g)),o)}function b(e,t){let{routesMeta:r}=e,o={},n="/",a=[];for(let e=0;e<r.length;++e){let s=r[e],i=e===r.length-1,c="/"===n?t:t.slice(n.length)||"/",l=_({path:s.relativePath,caseSensitive:s.caseSensitive,end:i},c);if(!l)return null;Object.assign(o,l.params);let u=s.route;a.push({params:o,pathname:j([n,l.pathname]),pathnameBase:M(j([n,l.pathnameBase])),route:u}),"/"!==l.pathnameBase&&(n=j([n,l.pathnameBase]))}return a}function _(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[r,o]=function(e,t,r){void 0===t&&(t=!1),void 0===r&&(r=!0);let o=[],n="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^$?{}|()[\]]/g,"\\$&").replace(/:(\w+)/g,((e,t)=>(o.push(t),"([^\\/]+)")));return e.endsWith("*")?(o.push("*"),n+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n+=r?"\\/*$":"(?:(?=[.~-]|%[0-9A-F]{2})|\\b|\\/|$)",[new RegExp(n,t?void 0:"i"),o]}(e.path,e.caseSensitive,e.end),n=t.match(r);if(!n)return null;let a=n[0],s=a.replace(/(.)\/+$/,"$1"),i=n.slice(1);return{params:o.reduce(((e,t,r)=>{if("*"===t){let e=i[r]||"";s=a.slice(0,a.length-e.length).replace(/(.)\/+$/,"$1")}return e[t]=function(e){try{return decodeURIComponent(e)}catch(t){return e}}(i[r]||""),e}),{}),pathname:a,pathnameBase:s,pattern:e}}function x(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=e.charAt(t.length);return r&&"/"!==r?null:e.slice(t.length)||"/"}const j=e=>e.join("/").replace(/\/\/+/g,"/"),M=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),S=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",A=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";function C(){return null!=(0,a.useContext)(i)}function k(){return C()||l(!1),(0,a.useContext)(i).location}function N(e){C()||l(!1);let{pathname:t}=k();return(0,a.useMemo)((()=>_(e,t)),[t,e])}function E(){C()||l(!1);let{basename:e,navigator:t}=(0,a.useContext)(s),{matches:r}=(0,a.useContext)(c),{pathname:o}=k(),i=JSON.stringify(r.map((e=>e.pathnameBase))),u=(0,a.useRef)(!1);return(0,a.useEffect)((()=>{u.current=!0})),(0,a.useCallback)((function(r,a){if(void 0===a&&(a={}),!u.current)return;if("number"==typeof r)return void t.go(r);let s=function(e,t,r){let o,a="string"==typeof e?n(e):e,s=""===e||""===a.pathname?"/":a.pathname;if(null==s)o=r;else{let e=t.length-1;if(s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let i=function(e,t){void 0===t&&(t="/");let{pathname:r,search:o="",hash:a=""}="string"==typeof e?n(e):e,s=r?r.startsWith("/")?r:function(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?r.length>1&&r.pop():"."!==e&&r.push(e)})),r.length>1?r.join("/"):"/"}(r,t):t;return{pathname:s,search:S(o),hash:A(a)}}(a,o);return s&&"/"!==s&&s.endsWith("/")&&!i.pathname.endsWith("/")&&(i.pathname+="/"),i}(r,JSON.parse(i),o);"/"!==e&&(s.pathname=j([e,s.pathname])),(a.replace?t.replace:t.push)(s,a.state)}),[e,t,i,o])}function I(){let{matches:e}=(0,a.useContext)(c),t=e[e.length-1];return t?t.params:{}}function T(e){l(!1)}function P(e){let{basename:t="/",children:r=null,location:c,navigationType:u=o.Pop,navigator:d,static:p=!1}=e;C()&&l(!1);let m=M(t),h=(0,a.useMemo)((()=>({basename:m,navigator:d,static:p})),[m,d,p]);"string"==typeof c&&(c=n(c));let{pathname:f="/",search:g="",hash:y="",state:w=null,key:v="default"}=c,b=(0,a.useMemo)((()=>{let e=x(f,m);return null==e?null:{pathname:e,search:g,hash:y,state:w,key:v}}),[m,f,g,y,w,v]);return null==b?null:(0,a.createElement)(s.Provider,{value:h},(0,a.createElement)(i.Provider,{children:r,value:{location:b,navigationType:u}}))}function O(e){let{children:t,location:r}=e;return function(e,t){C()||l(!1);let{matches:r}=(0,a.useContext)(c),o=r[r.length-1],s=o?o.params:{},i=(o&&o.pathname,o?o.pathnameBase:"/");o&&o.route;let d,p=k();if(t){var m;let e="string"==typeof t?n(t):t;"/"===i||(null==(m=e.pathname)?void 0:m.startsWith(i))||l(!1),d=e}else d=p;let h=d.pathname||"/",f=u(e,{pathname:"/"===i?h:h.slice(i.length)||"/"});return function(e,t){return void 0===t&&(t=[]),null==e?null:e.reduceRight(((r,o,n)=>(0,a.createElement)(c.Provider,{children:void 0!==o.route.element?o.route.element:r,value:{outlet:r,matches:t.concat(e.slice(0,n+1))}})),null)}(f&&f.map((e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:j([i,e.pathname]),pathnameBase:"/"===e.pathnameBase?i:j([i,e.pathnameBase])}))),r)}(D(t),r)}function D(e){let t=[];return a.Children.forEach(e,(e=>{if(!(0,a.isValidElement)(e))return;if(e.type===a.Fragment)return void t.push.apply(t,D(e.props.children));e.type!==T&&l(!1);let r={caseSensitive:e.props.caseSensitive,element:e.props.element,index:e.props.index,path:e.props.path};e.props.children&&(r.children=D(e.props.children)),t.push(r)})),t}},94931:(e,t,r)=>{"use strict";var o=r(51609),n=Symbol.for("react.element"),a=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,i=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function l(e,t,r){var o,a={},l=null,u=null;for(o in void 0!==r&&(l=""+r),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(u=t.ref),t)s.call(t,o)&&!c.hasOwnProperty(o)&&(a[o]=t[o]);if(e&&e.defaultProps)for(o in t=e.defaultProps)void 0===a[o]&&(a[o]=t[o]);return{$$typeof:n,type:e,key:l,ref:u,props:a,_owner:i.current}}t.Fragment=a,t.jsx=l,t.jsxs=l},39793:(e,t,r)=>{"use strict";e.exports=r(94931)},73745:(e,t,r)=>{"use strict";var o=r(40885),n=r(91555),a=r(87612)(),s=r(8632),i=o("%TypeError%"),c=o("%Math.floor%");e.exports=function(e,t){if("function"!=typeof e)throw new i("`fn` is not a function");if("number"!=typeof t||t<0||t>4294967295||c(t)!==t)throw new i("`length` must be a positive 32-bit integer");var r=arguments.length>2&&!!arguments[2],o=!0,l=!0;if("length"in e&&s){var u=s(e,"length");u&&!u.configurable&&(o=!1),u&&!u.writable&&(l=!1)}return(o||l||!r)&&(a?n(e,"length",t,!0,!0):n(e,"length",t)),e}},2435:(e,t,r)=>{"use strict";var o=r(40885),n=r(40368),a=r(83282),s=o("%TypeError%"),i=o("%WeakMap%",!0),c=o("%Map%",!0),l=n("WeakMap.prototype.get",!0),u=n("WeakMap.prototype.set",!0),d=n("WeakMap.prototype.has",!0),p=n("Map.prototype.get",!0),m=n("Map.prototype.set",!0),h=n("Map.prototype.has",!0),f=function(e,t){for(var r,o=e;null!==(r=o.next);o=r)if(r.key===t)return o.next=r.next,r.next=e.next,e.next=r,r};e.exports=function(){var e,t,r,o={assert:function(e){if(!o.has(e))throw new s("Side channel does not contain "+a(e))},get:function(o){if(i&&o&&("object"==typeof o||"function"==typeof o)){if(e)return l(e,o)}else if(c){if(t)return p(t,o)}else if(r)return function(e,t){var r=f(e,t);return r&&r.value}(r,o)},has:function(o){if(i&&o&&("object"==typeof o||"function"==typeof o)){if(e)return d(e,o)}else if(c){if(t)return h(t,o)}else if(r)return function(e,t){return!!f(e,t)}(r,o);return!1},set:function(o,n){i&&o&&("object"==typeof o||"function"==typeof o)?(e||(e=new i),u(e,o,n)):c?(t||(t=new c),m(t,o,n)):(r||(r={key:{},next:null}),function(e,t,r){var o=f(e,t);o?o.value=r:e.next={key:t,next:e.next,value:r}}(r,o,n))}};return o}},51609:e=>{"use strict";e.exports=window.React},75795:e=>{"use strict";e.exports=window.ReactDOM},66087:e=>{"use strict";e.exports=window.lodash},76154:e=>{"use strict";e.exports=window.moment},45155:e=>{"use strict";e.exports=window.wc.adminLayout},98846:e=>{"use strict";e.exports=window.wc.components},91554:e=>{"use strict";e.exports=window.wc.csvExport},94111:e=>{"use strict";e.exports=window.wc.currency},27752:e=>{"use strict";e.exports=window.wc.customerEffortScore},40314:e=>{"use strict";e.exports=window.wc.data},77374:e=>{"use strict";e.exports=window.wc.date},14908:e=>{"use strict";e.exports=window.wc.experimental},750:e=>{"use strict";e.exports=window.wc.explat},96476:e=>{"use strict";e.exports=window.wc.navigation},43577:e=>{"use strict";e.exports=window.wc.number},47778:e=>{"use strict";e.exports=window.wc.productEditor},86817:e=>{"use strict";e.exports=window.wc.remoteLogging},83306:e=>{"use strict";e.exports=window.wc.tracks},15703:e=>{"use strict";e.exports=window.wc.wcSettings},20195:e=>{"use strict";e.exports=window.wp.a11y},1455:e=>{"use strict";e.exports=window.wp.apiFetch},63162:e=>{"use strict";e.exports=window.wp.blob},94715:e=>{"use strict";e.exports=window.wp.blockEditor},89363:e=>{"use strict";e.exports=window.wp.blockLibrary},74997:e=>{"use strict";e.exports=window.wp.blocks},56427:e=>{"use strict";e.exports=window.wp.components},29491:e=>{"use strict";e.exports=window.wp.compose},3582:e=>{"use strict";e.exports=window.wp.coreData},47143:e=>{"use strict";e.exports=window.wp.data},66161:e=>{"use strict";e.exports=window.wp.dataControls},38443:e=>{"use strict";e.exports=window.wp.date},64040:e=>{"use strict";e.exports=window.wp.deprecated},28107:e=>{"use strict";e.exports=window.wp.dom},43656:e=>{"use strict";e.exports=window.wp.editor},86087:e=>{"use strict";e.exports=window.wp.element},52619:e=>{"use strict";e.exports=window.wp.hooks},18537:e=>{"use strict";e.exports=window.wp.htmlEntities},27723:e=>{"use strict";e.exports=window.wp.i18n},53031:e=>{"use strict";e.exports=window.wp.keyboardShortcuts},48558:e=>{"use strict";e.exports=window.wp.keycodes},16480:e=>{"use strict";e.exports=window.wp.mediaUtils},692:e=>{"use strict";e.exports=window.wp.notices},92279:e=>{"use strict";e.exports=window.wp.plugins},41233:e=>{"use strict";e.exports=window.wp.preferences},5573:e=>{"use strict";e.exports=window.wp.primitives},35434:e=>{"use strict";e.exports=window.wp.privateApis},71628:e=>{"use strict";e.exports=window.wp.router},93832:e=>{"use strict";e.exports=window.wp.url},70123:()=>{},4921:(e,t,r)=>{"use strict";function o(e){var t,r,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(r=o(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}r.d(t,{A:()=>n});const n=function(){for(var e,t,r=0,n="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=o(e))&&(n&&(n+=" "),n+=t);return n}}},a={};function s(e){var t=a[e];if(void 0!==t)return t.exports;var r=a[e]={exports:{}};return n[e].call(r.exports,r,r.exports,s),r.exports}s.m=n,s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,s.t=function(r,o){if(1&o&&(r=this(r)),8&o)return r;if("object"==typeof r&&r){if(4&o&&r.__esModule)return r;if(16&o&&"function"==typeof r.then)return r}var n=Object.create(null);s.r(n);var a={};e=e||[null,t({}),t([]),t(t)];for(var i=2&o&&r;"object"==typeof i&&!~e.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach((e=>a[e]=()=>r[e]));return a.default=()=>r,s.d(n,a),n},s.d=(e,t)=>{for(var r in t)s.o(t,r)&&!s.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},s.f={},s.e=e=>Promise.all(Object.keys(s.f).reduce(((t,r)=>(s.f[r](e,t),t)),[])),s.u=e=>"chunks/"+({8:"analytics-report",504:"product-page",592:"marketplace",823:"leaderboards",945:"dashboard",1133:"dashboard-charts",1438:"store-alerts",1494:"homescreen",2152:"core-profiler",2464:"activity-panels-setup",3151:"analytics-report-revenue",3256:"analytics-report-customers",3404:"customizable-dashboard",3678:"launch-store",4040:"customize-store",4409:"analytics-report-categories",5113:"analytics-report-stock",5845:"analytics-report-products",5941:"analytics-settings",6071:"analytics-report-variations",6115:"store-performance",6424:"analytics-report-downloads",6779:"edit-product-page",7202:"analytics-report-orders",7752:"multichannel-marketing",7956:"activity-panels-help",8013:"wcpay-payment-welcome-page",8039:"activity-panels-inbox",8068:"analytics-report-taxes",8286:"analytics-report-coupons"}[e]||e)+".js?ver="+{8:"ee89678f7bb18c3b43ba",53:"2a9fb3016b368d4973d7",504:"b63390bed114837536db",592:"154d0e7c64dbe0a8ac3c",624:"6e2148fdf07db6611ec8",823:"ea382212b576c7c09b7a",945:"6c791cb30483931a36c1",1133:"f7c18218b38080c1aee8",1438:"e14a1b76936d2b76ae49",1494:"647c9ef1f2d39d315a6a",1779:"46f4adcedc7670443dcb",2152:"79043a19edfd7d2abb71",2304:"aab454e2a264e44181e7",2464:"f7eb4622c09603e66cae",2672:"680ff411fde0a50850b7",2791:"4885f4279fb690222c82",2812:"7037b603787c7158dbc4",3040:"0381658d58e4f8646dd1",3151:"e3f4d2c86bb410b6e5ad",3222:"4c11c359080f8d00a39b",3240:"e875968cbf383cfdb9fe",3256:"09850737302852e082c8",3404:"e1b264b34658249d7dc6",3678:"c4ef141b3efbfb5765d5",3837:"b1aedd810699fd263d52",3970:"49fa294f673621e3d9b9",4040:"02d441ad0d1abe532ccc",4241:"7ec0da638e1a806c9d4b",4409:"72f5d4913f637c141c8d",4925:"95d9d226b9661fc8f713",5113:"9ef0e7b42091bd9337c4",5845:"8429ec2ab18cf699af84",5875:"6ef3b65f123e9fb643d0",5941:"3ff2a456859a6dd41e5e",5945:"230d5be694f4ba01e8f6",6071:"ed2b552396be322dc86a",6115:"4ebc80497255a7069446",6145:"09cd809ea049f1046223",6424:"1cefd8497ab686670391",6568:"9aefeb89ed565069fcfa",6779:"934819aeae0dc5847109",7202:"e0248a1562615fb69d48",7311:"41c04e16f6b432bc7060",7331:"38d0b850214518e4d4ec",7752:"2f369a9daa3390d34b23",7956:"5553b400608236f1316d",8013:"5535366570864af27488",8039:"7b8d213a62e02fd43e72",8068:"26a7dfe34e929d85ad6a",8276:"119ad8582a9f1b379900",8286:"2329fd8a2d5860eda94d",9568:"118303fdba74b42901c4",9670:"d592638b01a089e98868",9719:"1d8b6a12ef3178de7990",9994:"2bfe57964e976d1cbdd2"}[e],s.miniCssF=e=>"chunks/"+e+".style.css?ver="+{8:"334ee745e0724668c16d",53:"75c7541b8cb23dc351e3",504:"66dbcf109b478ee333fc",592:"4ad96c3d546b8caf226c",823:"9944f07038cfcf4c557f",945:"0fb147d4cd85ce5ca440",1133:"b491bf7240b9adf8c188",1438:"1a0ef7160a89435a3a20",1494:"3004cec16954b6016635",2069:"00db0a00ec414ea7944d",2152:"0f2f1cab8f4ebf5d356a",3151:"b627dad947e1bdb0781c",3256:"776819c89b5c86485e64",3678:"a958def11340551ee09c",4040:"19ddc1f206e14e63f0a9",4241:"4cf32095f267ef8be347",4409:"e18292b89d0d65594698",5113:"776819c89b5c86485e64",5845:"e18292b89d0d65594698",5875:"baabe1753a0027ffd2dd",5941:"58c5e13021299f432de8",6071:"776819c89b5c86485e64",6115:"52c6ec2b343f9e0b91e2",6424:"776819c89b5c86485e64",6779:"66dbcf109b478ee333fc",7202:"b627dad947e1bdb0781c",7311:"88a79bf0b361b4f9a077",7752:"c629f3b33eb0ea24a570",7956:"08abeb3d8724447da769",8013:"6de9a4277a46fb1e5807",8039:"7ac669c717c4eb7f1a0d",8068:"776819c89b5c86485e64",8286:"776819c89b5c86485e64"}[e],s.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r={},o="__wcAdmin_webpackJsonp:",s.l=(e,t,n,a)=>{if(r[e])r[e].push(t);else{var i,c;if(void 0!==n)for(var l=document.getElementsByTagName("script"),u=0;u<l.length;u++){var d=l[u];if(d.getAttribute("src")==e||d.getAttribute("data-webpack")==o+n){i=d;break}}i||(c=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,s.nc&&i.setAttribute("nonce",s.nc),i.setAttribute("data-webpack",o+n),i.src=e),r[e]=[t];var p=(t,o)=>{i.onerror=i.onload=null,clearTimeout(m);var n=r[e];if(delete r[e],i.parentNode&&i.parentNode.removeChild(i),n&&n.forEach((e=>e(o))),t)return t(o)},m=setTimeout(p.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=p.bind(null,i.onerror),i.onload=p.bind(null,i.onload),c&&document.head.appendChild(i)}},s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.j=3524,(()=>{var e;s.g.importScripts&&(e=s.g.location+"");var t=s.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var r=t.getElementsByTagName("script");if(r.length)for(var o=r.length-1;o>-1&&(!e||!/^http(s?):/.test(e));)e=r[o--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),s.p=e+"../"})(),(()=>{if("undefined"!=typeof document){var e={3524:0};s.f.miniCss=(t,r)=>{e[t]?r.push(e[t]):0!==e[t]&&{8:1,53:1,504:1,592:1,823:1,945:1,1133:1,1438:1,1494:1,2069:1,2152:1,3151:1,3256:1,3678:1,4040:1,4241:1,4409:1,5113:1,5845:1,5875:1,5941:1,6071:1,6115:1,6424:1,6779:1,7202:1,7311:1,7752:1,7956:1,8013:1,8039:1,8068:1,8286:1}[t]&&r.push(e[t]=(e=>new Promise(((t,r)=>{var o=s.miniCssF(e),n=s.p+o;if(((e,t)=>{for(var r=document.getElementsByTagName("link"),o=0;o<r.length;o++){var n=(s=r[o]).getAttribute("data-href")||s.getAttribute("href");if("stylesheet"===s.rel&&(n===e||n===t))return s}var a=document.getElementsByTagName("style");for(o=0;o<a.length;o++){var s;if((n=(s=a[o]).getAttribute("data-href"))===e||n===t)return s}})(o,n))return t();((e,t,r,o,n)=>{var a=document.createElement("link");a.rel="stylesheet",a.type="text/css",s.nc&&(a.nonce=s.nc),a.onerror=a.onload=r=>{if(a.onerror=a.onload=null,"load"===r.type)o();else{var s=r&&r.type,i=r&&r.target&&r.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+s+": "+i+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=s,c.request=i,a.parentNode&&a.parentNode.removeChild(a),n(c)}},a.href=t,document.head.appendChild(a)})(e,n,0,t,r)})))(t).then((()=>{e[t]=0}),(r=>{throw delete e[t],r})))}}})(),(()=>{var e={3524:0};s.f.j=(t,r)=>{var o=s.o(e,t)?e[t]:void 0;if(0!==o)if(o)r.push(o[2]);else if(2069!=t){var n=new Promise(((r,n)=>o=e[t]=[r,n]));r.push(o[2]=n);var a=s.p+s.u(t),i=new Error;s.l(a,(r=>{if(s.o(e,t)&&(0!==(o=e[t])&&(e[t]=void 0),o)){var n=r&&("load"===r.type?"missing":r.type),a=r&&r.target&&r.target.src;i.message="Loading chunk "+t+" failed.\n("+n+": "+a+")",i.name="ChunkLoadError",i.type=n,i.request=a,o[1](i)}}),"chunk-"+t,t)}else e[t]=0};var t=(t,r)=>{var o,n,[a,i,c]=r,l=0;if(a.some((t=>0!==e[t]))){for(o in i)s.o(i,o)&&(s.m[o]=i[o]);c&&c(s)}for(t&&t(r);l<a.length;l++)n=a[l],s.o(e,n)&&e[n]&&e[n][0](),e[n]=0},r=globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),(()=>{"use strict";var e=s(86087),t=s(40314),r=s(11315),o=(s(89677),s(27752)),n=s(91244),a=s.n(n),i=s(39793);const c=a()("wc-admin:client");var l=s(29491),u=s(47143),d=s(33068),p=s(48808),m=s(66087),h=s(96476);window.wc.notices;var f=s(56427),g=s(83306),y=s(92279),w=s(45155),v=s(27723),b=s(24148),_=s(45521),x=s(6513),j=s(98846),M=s(14908);const S=()=>(0,i.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsx)("mask",{id:"mask0_2915:6733",maskUnits:"userSpaceOnUse",x:"4",y:"3",width:"16",height:"18",children:(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.5 3.5H13.5L13.9 5.5H19.5V15.5H12.5L12.1 13.5H6.5V20.5H4.5V3.5ZM12.26 7.5L11.86 5.5H6.5V11.5H13.74L14.14 13.5H17.5V7.5H12.26Z",fill:"white"})}),(0,i.jsx)("g",{mask:"url(#mask0_2915:6733)",children:(0,i.jsx)("rect",{width:"24",height:"24",fill:"#50575E"})})]});var A=s(46591),C=s(56109);const k={page:1,per_page:t.QUERY_DEFAULTS.pageSize,status:"unactioned",type:t.QUERY_DEFAULTS.noteTypes,orderby:"date",order:"desc"};function N(e){const{getNotes:r,getNotesError:o,isResolving:n}=e(t.notesStore),{getCurrentUser:a}=e(t.userStore),s=a(),i=parseInt(s&&s.woocommerce_meta&&s.woocommerce_meta.activity_panel_inbox_last_read,10);if(!i)return null;r(k);const c=Boolean(o("getNotes",[k])),l=n("getNotes",[k]);if(c||l)return null;const u=r(k);return(0,A.kT)(u,i)>0}var E=s(4921);const I=({icon:e,title:t,name:r,unread:o,selected:n,isPanelOpen:a,onTabClick:s})=>{const c=(0,E.A)("woocommerce-layout__activity-panel-tab",{"is-active":a&&n,"has-unread":o}),l=`activity-panel-tab-${r}`,u=!t&&r?r.charAt(0).toUpperCase()+r.slice(1):void 0;return(0,i.jsxs)(f.Button,{role:"tab",className:c,"aria-selected":n,"aria-controls":`activity-panel-${r}`,id:l,"data-testid":l,"aria-label":u,onClick:()=>{s(r)},children:[e,t," ",o&&(0,i.jsx)("span",{className:"screen-reader-text",children:(0,v.__)("unread activity","woocommerce")})]},l)},T=({tabs:t,onTabClick:r,selectedTab:o,tabOpen:n=!1})=>{const[{tabOpen:a,currentTab:s},c]=(0,e.useState)({tabOpen:n,currentTab:o});return(0,e.useEffect)((()=>{c({tabOpen:n,currentTab:o})}),[n,o]),(0,i.jsx)(f.NavigableMenu,{role:"tablist",orientation:"horizontal",className:"woocommerce-layout__activity-panel-tabs",children:t&&t.map(((e,t)=>{if(e.component){const{component:r,options:o}=e;return(0,i.jsx)(r,{...o},t)}return(0,i.jsx)(I,{index:t,isPanelOpen:a,selected:s===e.name,...e,onTabClick:()=>{const t=s!==e.name&&""!==s||!a;t&&s===e.name||(0,g.recordEvent)("activity_panel_open",{tab:e.name}),c({tabOpen:t,currentTab:e.name}),r(e,t)}},t)}))})},P=({setupTasksComplete:e,setupCompletePercent:t})=>(0,i.jsxs)("svg",{className:"woocommerce-layout__activity-panel-tab-icon setup-progress",viewBox:"0 0 25 25",children:[(0,i.jsx)("path",{className:"setup-progress-ring",d:"M 12.476 23.237 C 18.369 23.237 23.146 18.414 23.146 12.464 C 23.146 6.512 18.369 1.687 12.476 1.687 C 6.581 1.687 1.803 6.512 1.803 12.464 C 1.803 18.414 6.581 23.237 12.476 23.237 Z"}),(0,i.jsx)("path",{className:"setup-progress-slice",transform:"matrix(-0.034188, 0, 0, 0.034134, 38.373184, -8.278505)",d:"M 522 607 A 237 237 0 0 1 759 370 L 759 607 Z",fill:e>0?"currentColor":"white"}),(0,i.jsx)("path",{className:"setup-progress-slice",transform:"matrix(-0.034188, 0, 0, -0.034134, 38.368454, 33.13131)",d:"M 522 607 A 237 237 0 0 1 759 370 L 759 607 Z",fill:t>=50?"currentColor":"white"}),(0,i.jsx)("path",{className:"setup-progress-slice",transform:"matrix(0.034188, 0, 0, -0.034134, -13.500516, 33.133827)",d:"M 522 607 A 237 237 0 0 1 759 370 L 759 607 Z",fill:t>=75?"currentColor":"white"}),(0,i.jsx)("path",{className:"setup-progress-slice",transform:"matrix(0.034188, 0, 0, 0.034134, -13.495783, -8.281025)",d:"M 522 607 A 237 237 0 0 1 759 370 L 759 607 Z",fill:"white"})]});var O=s(63861),D=s(28107);const L=["button","submit"];const R=({content:t,isPanelOpen:r,isPanelSwitching:o,currentTab:n,tab:a,closePanel:s,clearPanel:c})=>{const l="woocommerce-layout__activity-panel-wrapper",u=function(t="firstElement"){const r=(0,e.useRef)(t);return(0,e.useEffect)((()=>{r.current=t}),[t]),(0,e.useCallback)((e=>{if(!e||!1===r.current)return;if(e.contains(e.ownerDocument.activeElement))return;let t=e;if("firstElement"===r.current){const r=D.focus.tabbable.find(e)[0];r&&(t=r)}t.focus()}),[])}(),d=(0,e.useRef)(null),p=function(t){const r=(0,e.useRef)(t);(0,e.useEffect)((()=>{r.current=t}),[t]);const o=(0,e.useRef)(!1),n=(0,e.useRef)(),a=(0,e.useCallback)((()=>{clearTimeout(n.current)}),[]);(0,e.useEffect)((()=>()=>a()),[]),(0,e.useEffect)((()=>{t||a()}),[t,a]);const s=(0,e.useCallback)((e=>{const{type:t,target:r}=e;(0,m.includes)(["mouseup","touchend"],t)?o.current=!1:function(e){if(!(e instanceof window.HTMLElement))return!1;switch(e.nodeName){case"A":case"BUTTON":return!0;case"INPUT":return(0,m.includes)(L,e.type)}return!1}(r)&&(o.current=!0)}),[]),i=(0,e.useCallback)((e=>{e.persist(),o.current||(n.current=setTimeout((()=>{document.hasFocus()?"function"==typeof r.current&&r.current(e):e.preventDefault()}),0))}),[]);return{onFocus:a,onMouseDown:s,onMouseUp:s,onTouchStart:s,onTouchEnd:s,onBlur:i}}((e=>{const t=e.relatedTarget&&(e.relatedTarget.closest(".woocommerce-inbox-dismiss-confirmation_modal")||e.relatedTarget.closest(".components-snackbar__action"));r&&!t&&s()})),h=(0,e.useCallback)((e=>{d.current=e,u(e)}),[]);if(!a)return(0,i.jsx)("div",{className:l});if(!t)return null;const f=(0,E.A)(l,{"is-open":r,"is-switching":o});return(0,i.jsx)("div",{className:f,tabIndex:0,role:"tabpanel","aria-label":a.title,onTransitionEnd:e=>{e&&"transform"===e.propertyName&&(c(),d.current&&r&&a&&u(d.current))},...p,ref:h,children:(0,i.jsx)("div",{className:"woocommerce-layout__activity-panel-content",id:"activity-panel-"+n,children:(0,i.jsx)(e.Suspense,{fallback:(0,i.jsx)(j.Spinner,{}),children:t})},"activity-panel-"+n)})};var z=s(29332),F=s(42288),U=s(57882),V=s(24060),B=s(55177);const Q=({onClose:t})=>(0,i.jsx)(j.TourKit,{config:{placement:"bottom",options:{effects:{arrowIndicator:!0,autoScroll:{behavior:"auto",block:"center"},liveResize:{mutation:!0,resize:!0,rootElementSelector:"#wpwrap"}},popperModifiers:[{name:"auto",enabled:!0,phase:"beforeWrite",requires:["computeStyles"]},{name:"offset",options:{offset:()=>[52,16]}}],classNames:"woocommerce-lys-homescreen-status-tour-kit"},steps:[{referenceElements:{desktop:"#wp-admin-bar-woocommerce-site-visibility-badge"},meta:{name:"set-your-store-visibility",heading:(0,v.__)("Set your store’s visibility","woocommerce"),descriptions:{desktop:(0,e.createInterpolateElement)((0,v.__)('Launch your store only when you’re ready to by switching between "Coming soon" and "Live" modes. Build excitement by creating a custom page visitors will see before you’re ready to go live. <link>Discover more</link>',"woocommerce"),{link:(0,i.jsx)("a",{href:"https://woocommerce.com/document/configuring-woocommerce-settings/coming-soon-mode/",target:"_blank",rel:"noreferrer"})})}}}],closeHandler:t}});var H=s(51609);const G=()=>{const{showTour:e,setShowTour:r,onClose:o,shouldTourBeShown:n}=(()=>{const[e,r]=(0,H.useState)(!0),o=(0,u.useSelect)((e=>"yes"===e(t.optionsStore).getOption("woocommerce_show_lys_tour")),[]),n=(0,u.useSelect)((e=>{const t=e("core").getCurrentUser();return!t||"yes"===t.meta.woocommerce_launch_your_store_tour_hidden}),[]),{launch_your_store_tour_hidden:a,updateUserPreferences:s}=(0,t.useUserPreferences)();return{onClose:()=>{s({launch_your_store_tour_hidden:"yes"})},shouldTourBeShown:o&&!(n||"yes"===a),showTour:e,setShowTour:r}})();return(0,i.jsx)("div",{className:"woocommerce-lys-status",children:n&&e&&(0,i.jsx)(Q,{onClose:()=>{o(),r(!1)}})})},Y=({enabled:e}={enabled:!0})=>{const{isLoading:r,launchYourStoreEnabled:o,comingSoon:n,storePagesOnly:a,privateLink:s,shareKey:i}=(0,u.useSelect)((r=>{if(!e)return{isLoading:!1,comingSoon:null,storePagesOnly:null,privateLink:null,shareKey:null,launchYourStoreEnabled:null};const{hasFinishedResolution:o,getOption:n}=r(t.optionsStore);return{isLoading:!(o("getOption",["woocommerce_coming_soon"])||o("getOption",["woocommerce_store_pages_only"])||o("getOption",["woocommerce_private_link"])||o("getOption",["woocommerce_share_key"])),comingSoon:n("woocommerce_coming_soon"),storePagesOnly:n("woocommerce_store_pages_only"),privateLink:n("woocommerce_private_link"),shareKey:n("woocommerce_share_key"),launchYourStoreEnabled:window.wcAdminFeatures["launch-your-store"]}}),[e]);return{isLoading:r,comingSoon:n,storePagesOnly:a,privateLink:s,shareKey:i,launchYourStoreEnabled:o}};var W=s(15703);s(3582);const $="__EXPERIMENTAL__WcAdminSettingsSlots",{Slot:q}=(0,f.createSlotFill)($),{Fill:Z}=(0,f.createSlotFill)($);var J=s(99915),K=s(10790);const X=(0,e.lazy)((()=>Promise.all([s.e(9719),s.e(7956)]).then(s.bind(s,85810)))),ee=(0,e.lazy)((()=>Promise.all([s.e(8276),s.e(8039)]).then(s.bind(s,16421)))),te=(0,e.lazy)((()=>Promise.all([s.e(3240),s.e(9994),s.e(5875),s.e(2464)]).then(s.bind(s,16081)))),re=({isEmbedded:r,query:n})=>{const a="wc-admin"===n.page&&!n.path,[s,c]=(0,e.useState)(""),[l,d]=(0,e.useState)(!1),[p,f]=(0,e.useState)(!1),[y,A]=(0,e.useState)(!1),{fills:k}=(0,M.useSlot)(U.JT),E=Boolean(k?.length),{comingSoon:I}=Y({enabled:a}),D=()=>{d(!0),f(!1)},L=()=>{p||(d(!1),A(!1),c(""))};(0,e.useEffect)((()=>(0,h.addHistoryListener)((()=>{D(),L()}))),[]);const Q=(0,w.useExtendLayout)("activity-panel"),H=(0,e.useCallback)(((e,r)=>{let o={};if("wc-admin"===n.page&&"appearance"===n.task){const{getTaskLists:n}=e(t.onboardingStore),a=n().reduce(((e,t)=>[...e,...t.tasks]),[]).find((e=>"appearance"===e.id));o={set_notice:r("woocommerce_demo_store_notice")?"Y":"N",create_homepage:!0===a?.additionalData?.hasHomepage?"Y":"N",upload_logo:a?.additionalData?.themeMods?.custom_logo?"Y":"N"}}return o}),[n.page,n.task]),G=(0,e.useCallback)(((e,t,r)=>{const o=(0,z.VJ)(e),n=!!t&&(0,z.xC)(e,o)>0,a=!!t&&(0,F.my)(e),s=!!t&&(0,z.G9)(e);return r>0||n||a||s||E}),[E]),{requestingTaskListOptions:W,setupTaskListComplete:$,setupTaskListHidden:q,setupTasksCount:Z,setupTasksCompleteCount:re,thingsToDoNextCount:oe}=(0,J.fK)(),{hasUnreadNotes:ne,hasAbbreviatedNotifications:ae,previewSiteBtnTrackData:se}=(0,u.useSelect)((e=>{const{getOption:r}=e(t.optionsStore);return{hasUnreadNotes:N(e),hasAbbreviatedNotifications:G(e,q,oe),previewSiteBtnTrackData:H(e,r)}}),[G,oe,q,H]),{showCesModal:ie}=(0,u.useDispatch)(o.STORE_KEY),{currentUserCan:ce}=(0,t.useUser)(),le=()=>{const[e]=function(e){var t;const r=e?.startsWith("/")?1:0,o=e?.endsWith("/")?-1:void 0;return null!==(t=e?.slice(r,o)?.split("/"))&&void 0!==t?t:[]}(n.path);return"add-product"===e||"product"===e},ue=()=>{const e=(0,V.al)(window.location.search);return r&&/post-new\.php$/.test(window.location.pathname)&&"product"===e?.post_type},de=()=>n.task&&!n.path&&(!0===W||!1===q&&!1===$),pe=()=>{const e={name:"activity",title:(0,v.__)("Activity","woocommerce"),icon:(0,i.jsx)(S,{}),unread:ne||ae,visible:(r||!a)&&!de()&&!le()&&ce("manage_woocommerce")},t={name:"feedback",title:(0,v.__)("Feedback","woocommerce"),icon:(0,i.jsx)(B.h,{}),onClick:()=>{c("feedback"),f(!0),ie({action:"product_feedback",title:(0,v.__)("How's your experience with the product editor?","woocommerce"),firstQuestion:(0,v.__)("The product editing screen is easy to use","woocommerce"),secondQuestion:(0,v.__)("The product editing screen's functionality meets my needs","woocommerce")},{onRecordScore:()=>{c(""),f(!1)},onCloseModal:()=>{c(""),f(!1)}},{type:"snackbar",icon:(0,i.jsx)("span",{children:"🌟"})})},visible:ue()},o={name:"setup",title:(0,v.__)("Finish setup","woocommerce"),icon:(0,i.jsx)(P,{setupTasksComplete:re,setupCompletePercent:Math.ceil(re/Z*100)}),visible:ce("manage_woocommerce")&&!W&&!q&&!$&&!a&&!le()},s={name:"help",icon:(0,i.jsx)(b.A,{icon:_.A}),visible:ce("manage_woocommerce")&&(a&&!r||de())},l={component:O.P,visible:ce("manage_woocommerce")&&!r&&a&&!de()},u={component:()=>(0,i.jsx)(K.A,{page:"wc-admin"}),visible:a};return[e,t,o,{name:"previewSite",title:(0,v.__)("Preview site","woocommerce"),icon:(0,i.jsx)(b.A,{icon:x.A}),visible:a&&"appearance"===n.task,onClick:()=>(window.open((0,C.Qk)("siteUrl")),(0,g.recordEvent)("wcadmin_tasklist_previewsite",se),null)},{name:"previewStore",title:"yes"===I&&(0,v.__)("Preview store","woocommerce")||(0,v.__)("View store","woocommerce"),visible:a&&"appearance"!==n.task,onClick:()=>(window.open((0,C.Qk)("shopUrl")),(0,g.recordEvent)("wcadmin_previewstore_click"),null)},l,u,s].filter((e=>e.visible))},me=pe(),he=(0,m.uniqueId)("activity-panel-header_");return(0,i.jsx)(w.LayoutContextProvider,{value:Q,children:(0,i.jsxs)("div",{children:[(0,i.jsx)(j.H,{id:he,className:"screen-reader-text",children:(0,v.__)("Store Activity","woocommerce")}),(0,i.jsxs)(j.Section,{component:"aside",id:"woocommerce-activity-panel",className:"woocommerce-layout__activity-panel","aria-labelledby":he,children:[(0,i.jsx)(T,{tabs:me,tabOpen:p,selectedTab:s,onTabClick:(e,t)=>{e.onClick?e.onClick():(({name:e},t)=>{const r=e!==s&&""!==s&&t&&p;l||(c(e),f(t),A(r))})(e,t)}}),(0,i.jsx)(R,{currentTab:!0,isPanelOpen:p,isPanelSwitching:y,tab:(0,m.find)(pe(),{name:s}),content:(e=>{const{task:t}=n;switch(e){case"activity":return(0,i.jsx)(ee,{hasAbbreviatedNotifications:ae,thingsToDoNextCount:oe});case"help":return(0,i.jsx)(X,{taskName:t});case"setup":return(0,i.jsx)(te,{query:n});default:return null}})(s),closePanel:()=>D(),clearPanel:()=>L()})]})]})})},oe=["wc-settings"];(0,y.registerPlugin)("activity-panel-header-item",{render:()=>(0,i.jsx)(w.WooHeaderItem,{order:20,children:({isEmbedded:e,query:t})=>!window.wcAdminFeatures["activity-panels"]||oe.includes(t.page)?null:(0,i.jsx)(re,{isEmbedded:e,query:t})}),scope:"woocommerce-admin"});var ne=s(8181);const ae="android",se=()=>(0,i.jsxs)("svg",{width:"37",height:"37",viewBox:"0 0 64 64",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsx)("rect",{width:"64",height:"64",rx:"12",fill:"#873EFF"}),(0,i.jsxs)("g",{clipPath:"url(#clip0_578_2937)",children:[(0,i.jsx)("path",{d:"M13.068 38.3129C14.5525 38.3129 15.7438 37.5723 16.6418 35.8688L18.6395 32.0916V35.2948C18.6395 37.1834 19.8491 38.3129 21.7185 38.3129C23.1847 38.3129 24.266 37.6649 25.3107 35.8688L29.9109 28.0181C30.9189 26.2961 30.2041 25 27.9865 25C26.7952 25 26.0255 25.3888 25.329 26.7035L22.1584 32.7211V27.37C22.1584 25.7777 21.407 25 20.0141 25C18.9144 25 18.0347 25.4814 17.3566 26.8146L14.3692 32.7211V27.4256C14.3692 25.7221 13.6728 25 11.9867 25H8.54111C7.23986 25 6.58008 25.611 6.58008 26.7405C6.58008 27.87 7.27652 28.518 8.54111 28.518H9.95232V35.2763C9.95232 37.1834 11.2169 38.3129 13.068 38.3129Z",fill:"white"}),(0,i.jsx)("path",{d:"M36.6384 25C32.8813 25 30.0039 27.8329 30.0039 31.6657C30.0039 35.4985 32.8996 38.3129 36.6384 38.3129C40.3772 38.3129 43.2363 35.48 43.2546 31.6657C43.2546 27.8329 40.3772 25 36.6384 25ZM36.6384 34.2209C35.2272 34.2209 34.2559 33.147 34.2559 31.6657C34.2559 30.1844 35.2272 29.092 36.6384 29.092C38.0496 29.092 39.021 30.1844 39.021 31.6657C39.021 33.147 38.068 34.2209 36.6384 34.2209Z",fill:"white"}),(0,i.jsx)("path",{d:"M50.8096 25C47.0708 25 44.1934 27.8329 44.1934 31.6657C44.1934 35.4985 47.0708 38.3129 50.8096 38.3129C54.5483 38.3129 57.4257 35.48 57.4257 31.6657C57.4257 27.8514 54.5483 25 50.8096 25ZM50.8096 34.2209C49.38 34.2209 48.4453 33.147 48.4453 31.6657C48.4453 30.1844 49.3983 29.092 50.8096 29.092C52.2208 29.092 53.1921 30.1844 53.1921 31.6657C53.1921 33.147 52.2208 34.2209 50.8096 34.2209Z",fill:"white"})]}),(0,i.jsx)("defs",{children:(0,i.jsx)("clipPath",{id:"clip0_578_2937",children:(0,i.jsx)("rect",{width:"50.8402",height:"13.3314",fill:"white",transform:"translate(6.58008 25)"})})})]}),ie="wcadmin_mobile_android_banner_click",ce="woocommerce-layout__show-app-banner",le=({onInstall:t,onDismiss:r})=>{const[o,n]=(0,e.useState)(!1),a=(/iPhone|iPad|iPod/i.test(window.navigator.userAgent)?"ios":/Android/i.test(window.navigator.userAgent)?ae:"unknown")===ae&&!o;return(0,e.useEffect)((()=>{const e=document.getElementsByClassName("woocommerce-layout")[0];return a&&e&&e.classList.add(ce),()=>{e&&e.classList.remove(ce)}}),[a]),a?(0,i.jsxs)("div",{className:"woocommerce-mobile-app-banner",children:[(0,i.jsx)(b.A,{icon:(0,i.jsx)(ne.A,{"data-testid":"dismiss-btn"}),onClick:()=>{r(),n(!0),(0,g.recordEvent)(ie,{action:"dismiss"})}}),(0,i.jsx)(se,{}),(0,i.jsxs)("div",{className:"woocommerce-mobile-app-banner__description",children:[(0,i.jsx)("p",{className:"woocommerce-mobile-app-banner__description__text",children:(0,v.__)("Run your store from anywhere","woocommerce")}),(0,i.jsx)("p",{className:"woocommerce-mobile-app-banner__description__text",children:(0,v.__)("Download the WooCommerce app","woocommerce")})]}),(0,i.jsx)(f.Button,{href:"https://play.google.com/store/apps/details?id=com.woocommerce.android",isSecondary:!0,onClick:()=>{t(),n(!0),(0,g.recordEvent)(ie,{action:"install"})},children:(0,v.__)("Install","woocommerce")})]}):null};(0,y.registerPlugin)("mobile-banner-header-item",{render:()=>{const{updateUserPreferences:e,...r}=(0,t.useUserPreferences)(),o=()=>{e({android_app_banner_dismissed:"yes"})};return"yes"===r.android_app_banner_dismissed?null:(0,i.jsx)(w.WooHeaderItem,{children:(0,i.jsx)(le,{onDismiss:o,onInstall:o})})},scope:"woocommerce-admin"});class ue extends e.Component{render(){return(0,i.jsx)("div",{id:"woocommerce-layout__notice-list",className:"woocommerce-layout__notice-list"})}}const de=ue,pe=(0,e.lazy)((()=>Promise.all([s.e(3240),s.e(1438)]).then(s.bind(s,46189)))),me=({children:t,showStoreAlerts:r=!0,showNotices:o=!0})=>(0,i.jsxs)("div",{className:"woocommerce-layout__primary",id:"woocommerce-layout__primary",children:[window.wcAdminFeatures["store-alerts"]&&r&&(0,i.jsx)(e.Suspense,{fallback:null,children:(0,i.jsx)(pe,{})}),o&&(0,i.jsx)(de,{}),t]});var he=s(4594),fe=s(52619),ge=s(54574),ye=s(51684),we=s(11357);const ve=(0,e.lazy)((()=>Promise.all([s.e(3970),s.e(6779)]).then(s.bind(s,67289)))),be=(0,e.lazy)((()=>Promise.all([s.e(3970),s.e(504)]).then(s.bind(s,67698)))),_e=(0,e.lazy)((()=>s.e(8).then(s.bind(s,26016)))),xe=(0,e.lazy)((()=>s.e(5941).then(s.bind(s,5214)))),je=(0,e.lazy)((()=>s.e(945).then(s.bind(s,40125)))),Me=(0,e.lazy)((()=>Promise.all([s.e(3240),s.e(8276),s.e(9719),s.e(9568),s.e(9994),s.e(5875),s.e(1494)]).then(s.bind(s,71503)))),Se=(0,e.lazy)((()=>Promise.all([s.e(3240),s.e(7752)]).then(s.bind(s,59810)))),Ae=(0,e.lazy)((()=>Promise.all([s.e(3240),s.e(2069),s.e(592)]).then(s.bind(s,49428)))),Ce=(0,e.lazy)((()=>Promise.all([s.e(3240),s.e(624),s.e(6145),s.e(9994),s.e(2152)]).then(s.bind(s,54500)))),ke=(0,e.lazy)((()=>Promise.all([s.e(3240),s.e(9994),s.e(8013)]).then(s.bind(s,69403)))),Ne=(0,e.lazy)((()=>Promise.all([s.e(5945),s.e(2672),s.e(9994),s.e(2069),s.e(3040),s.e(4040)]).then(s.bind(s,25575)))),Ee=(0,e.lazy)((()=>Promise.all([s.e(3240),s.e(3222),s.e(624),s.e(7331),s.e(9994),s.e(53),s.e(3040),s.e(3678)]).then(s.bind(s,85279)))),Ie="woocommerce_admin_pages_list",Te=()=>{const e=[],t=[["",(0,C.Qk)("woocommerceTranslation")]];if(e.push({container:Me,path:"/",breadcrumbs:[...t,(0,v.__)("Home","woocommerce")],wpOpenMenu:"toplevel_page_woocommerce",navArgs:{id:"woocommerce-home"},capability:"manage_woocommerce"}),window.wcAdminFeatures.analytics&&(e.push({container:je,path:"/analytics/overview",breadcrumbs:[...t,["/analytics/overview",(0,v.__)("Analytics","woocommerce")],(0,v.__)("Overview","woocommerce")],wpOpenMenu:"toplevel_page_wc-admin-path--analytics-overview",navArgs:{id:"woocommerce-analytics-overview"},capability:"view_woocommerce_reports"}),e.push({container:xe,path:"/analytics/settings",breadcrumbs:[...t,["/analytics/revenue",(0,v.__)("Analytics","woocommerce")],(0,v.__)("Settings","woocommerce")],wpOpenMenu:"toplevel_page_wc-admin-path--analytics-overview",navArgs:{id:"woocommerce-analytics-settings"},capability:"view_woocommerce_reports"}),e.push({container:_e,path:"/customers",breadcrumbs:[...t,(0,v.__)("Customers","woocommerce")],wpOpenMenu:"toplevel_page_woocommerce",navArgs:{id:"woocommerce-analytics-customers"},capability:"view_woocommerce_reports"}),e.push({container:_e,path:"/analytics/:report",breadcrumbs:({match:e})=>{const r=(0,m.find)((0,ge.A)(),{report:e.params.report});return r?[...t,["/analytics/revenue",(0,v.__)("Analytics","woocommerce")],r.title]:[]},wpOpenMenu:"toplevel_page_wc-admin-path--analytics-overview",capability:"view_woocommerce_reports"})),window.wcAdminFeatures.marketing&&e.push({container:Se,path:"/marketing",breadcrumbs:[...t,["/marketing",(0,v.__)("Marketing","woocommerce")],(0,v.__)("Overview","woocommerce")],wpOpenMenu:"toplevel_page_woocommerce-marketing",navArgs:{id:"woocommerce-marketing-overview"},capability:"view_woocommerce_reports"}),(0,ye.isFeatureEnabled)("marketplace")&&e.push({container:Ae,layout:{header:!1},path:"/extensions",breadcrumbs:[["/extensions",(0,v.__)("Extensions","woocommerce")],(0,v.__)("Extensions","woocommerce")],wpOpenMenu:"toplevel_page_woocommerce",capability:"manage_woocommerce",navArgs:{id:"woocommerce-marketplace"}}),(0,ye.isFeatureEnabled)("product_block_editor")){const t={container:be,layout:{header:!1},wpOpenMenu:"menu-posts-product",capability:"manage_woocommerce"};e.push({...t,path:"/add-product",breadcrumbs:[["/add-product",(0,v.__)("Product","woocommerce")],(0,v.__)("Add New Product","woocommerce")],navArgs:{id:"woocommerce-add-product"}}),e.push({...t,path:"/product/:productId",breadcrumbs:[["/edit-product",(0,v.__)("Product","woocommerce")],(0,v.__)("Edit Product","woocommerce")],navArgs:{id:"woocommerce-edit-product"}})}e.push({container:ve,layout:{header:!1},path:"/product/:productId/variation/:variationId",breadcrumbs:[["/edit-product",(0,v.__)("Product","woocommerce")],(0,v.__)("Edit Product Variation","woocommerce")],navArgs:{id:"woocommerce-edit-product"},wpOpenMenu:"menu-posts-product",capability:"edit_products"}),window.wcAdminFeatures.onboarding&&e.push({container:Ce,path:"/setup-wizard",breadcrumbs:[...t,(0,v.__)("Profiler","woocommerce")],capability:"manage_woocommerce",layout:{header:!1,footer:!1,showNotices:!0,showStoreAlerts:!1,showPluginArea:!1}}),window.wcAdminFeatures["core-profiler"]&&e.push({container:Ce,path:"/profiler",breadcrumbs:[...t,(0,v.__)("Profiler","woocommerce")],capability:"manage_woocommerce"}),window.wcAdminFeatures["customize-store"]&&e.push({container:Ne,path:"/customize-store/*",breadcrumbs:[...t,(0,v.__)("Customize Your Store","woocommerce")],layout:{header:!1,footer:!0,showNotices:!0,showStoreAlerts:!1,showPluginArea:!1},capability:"manage_woocommerce"}),window.wcAdminFeatures["launch-your-store"]&&e.push({container:Ee,path:"/launch-your-store/*",breadcrumbs:[...t,(0,v.__)("Launch Your Store","woocommerce")],layout:{header:!1,footer:!0,showNotices:!0,showStoreAlerts:!1,showPluginArea:!1},capability:"manage_woocommerce"}),window.wcAdminFeatures["wc-pay-welcome-page"]&&e.push({container:ke,path:"/wc-pay-welcome-page",breadcrumbs:[["/wc-pay-welcome-page",(0,v.__)("WooPayments","woocommerce")],(0,v.__)("WooPayments","woocommerce")],navArgs:{id:"woocommerce-wc-pay-welcome-page"},wpOpenMenu:"toplevel_page_woocommerce-wc-pay-welcome-page",capability:"manage_woocommerce"});const r=(0,fe.applyFilters)(Ie,e);return r.push({container:we.N,path:"*",breadcrumbs:[...t,(0,v.__)("Not allowed","woocommerce")],wpOpenMenu:"toplevel_page_woocommerce"}),r},Pe=({...t})=>{const r=function(e){const t=(0,H.useRef)();return(0,H.useEffect)((()=>{t.current=e}),[e]),t.current}(t);(0,H.useEffect)((()=>{window.document.documentElement.scrollTop=0,window.document.body.classList.remove("woocommerce-admin-is-loading")}),[]),(0,H.useEffect)((()=>{if(r){const e=(0,m.omit)(r.query,"chartType","filter","paged"),o=(0,m.omit)(t.query,"chartType","filter","paged");r.query.paged>1&&!(0,m.isEqual)(e,o)&&(0,h.getHistory)().replace((0,h.getNewPath)({paged:1})),r.match.url!==t.match.url&&(window.document.documentElement.scrollTop=0)}}),[t,r]);const{page:o,match:n,query:a}=t,{url:s,params:c}=n;return window.wpNavMenuUrlUpdate(a),window.wpNavMenuClassChange(o,s),(0,i.jsx)(e.Suspense,{fallback:o.fallback?(0,i.jsx)(o.fallback,{}):(0,i.jsx)("div",{className:"woocommerce-layout__loading",children:(0,i.jsx)(j.Spinner,{})}),children:(0,i.jsx)(o.container,{params:c,path:s,pathMatch:o.path,query:a})})};window.wpNavMenuUrlUpdate=function(e){const t=(0,h.getPersistedQuery)(e),r=(0,h.getQueryExcludedScreens)(),o=(0,h.getQueryExcludedScreensUrlUpdate)(),n=document.querySelectorAll("#adminmenu a");Array.from(n).forEach((e=>function(e,t,r,o=[]){if((0,h.isWCAdmin)(e.href)){const n=(0,m.last)(e.href.split("?")),a=(0,he.parse)(n),s=a.path||"homescreen",i=(0,h.getScreenFromPath)(s),c=r.includes(i),l="admin.php?"+(0,he.stringify)(Object.assign(a,c?{}:t));e.href=l,o.includes(i)||(e.onclick=e=>{e.ctrlKey||e.metaKey||(e.preventDefault(),(0,h.getHistory)().push(l))})}}(e,t,r,o)))};const Oe=[],De=()=>{window.jQuery("#adminmenu .wp-menu-image, #wpadminbar .ab-item").each((function(){const e=window.jQuery(this),t=e.css("background-image");t&&-1!==t.indexOf("data:image/svg+xml;base64")&&Oe.push(e.parent().parent())}))};window.wpNavMenuClassChange=function(e,t){0===Oe.length&&De();const r=document.querySelector("#adminmenu");Array.from(r.getElementsByClassName("current")).forEach((e=>{e.classList.remove("current")})),Array.from(r.querySelectorAll(".wp-has-current-submenu")).forEach((function(e){e.classList.remove("wp-has-current-submenu","selected","wp-menu-open"),e.classList.add("wp-not-current-submenu")}));const o="/"===t?"admin.php?page=wc-admin":"admin.php?page=wc-admin&path="+encodeURIComponent(t);let n="/"===t?`li > a[href$="${o}"], li > a[href*="${o}?"]`:`li > a[href*="${o}"]`;const a=e.navArgs?.parentPath;if(a&&(n+=`, li > a[href*="${"/"===a?"admin.php?page=wc-admin":"admin.php?page=wc-admin&path="+encodeURIComponent(a)}"]`),Array.from(r.querySelectorAll(n)).forEach((e=>{e.parentElement.classList.add("current")})),e.wpOpenMenu){const t=r.querySelector(`#${e.wpOpenMenu}`);t&&(r.querySelectorAll(".wp-submenu").forEach((e=>{e.style.marginTop=""})),t.classList.remove("wp-not-current-submenu"),t.classList.add("wp-has-current-submenu","wp-menu-open","current"))}window.wp&&window.wp.svgPainter&&(Oe.forEach((e=>{const t=window.jQuery._data(e[0],"events")||{};t.mouseover&&t.mouseover.forEach((t=>{t.handler.toString().includes("paintElement")&&e.off("mouseenter",t.handler)})),t.mouseout&&t.mouseout.forEach((t=>{t.handler.toString().includes("paintElement")&&e.off("mouseleave",t.handler)}))})),window.wp.svgPainter.paint());const s=document.querySelector("#wpwrap");s&&s.classList.contains("wp-responsive-open")&&s.classList.remove("wp-responsive-open")};var Le=s(18537),Re=s(76861),ze=s(11846);function Fe(){return window.innerHeight+window.scrollY>=document.body.scrollHeight}function Ue(){const[t,r]=(0,e.useState)(!1),[o,n]=(0,e.useState)(Fe()),a=(0,e.useRef)(null);return(0,e.useEffect)((()=>{const e=()=>{r(window.pageYOffset>20),n(Fe())},t=()=>{a.current=window.requestAnimationFrame(e)};return window.addEventListener("scroll",t),window.addEventListener("resize",t),()=>{window.removeEventListener("scroll",t),window.removeEventListener("resize",t),window.cancelAnimationFrame(a.current)}}),[]),{isScrolled:t,atBottom:o,atTop:!t}}const Ve=e=>{let t;return t=e.length>2&&Array.isArray(e[1])&&["admin.php?page=wc-settings","admin.php?page=wc-reports","admin.php?page=wc-status"].includes(e[1][0])?e[1][1]:e[e.length-1],t},Be=({isEmbedded:t,query:r,showReminderBar:o,sections:n,children:a,leftAlign:s=!0})=>{const{isScrolled:c}=Ue(),l=(0,e.useRef)(null),u=(0,M.useSlot)(w.WC_HEADER_PAGE_TITLE_SLOT_NAME),d=Boolean(u?.fills?.length),p=(({headerElement:t,headerItemSlot:r})=>{const o=(0,e.useRef)(null),n=(0,e.useCallback)((()=>{o.current&&clearTimeout(o.current),o.current=setTimeout((function(){const e=document.querySelector("#wpbody");e&&t.current&&(e.style.marginTop=`${t.current.clientHeight}px`)}),200)}),[t]);return(0,e.useLayoutEffect)((()=>(n(),window.addEventListener("resize",n),()=>{window.removeEventListener("resize",n);const e=document.querySelector("#wpbody");e&&(e.style.marginTop="")})),[r?.fills,n]),n})({headerElement:l,headerItemSlot:(0,M.useSlot)(w.WC_HEADER_SLOT_NAME)});return(0,i.jsxs)("div",{className:(0,E.A)("woocommerce-layout__header",{"is-scrolled":c}),ref:l,children:[o&&(0,i.jsx)(ze.O,{updateBodyMargin:p,taskListId:"setup"}),(0,i.jsxs)("div",{className:"woocommerce-layout__header-wrapper",children:[(0,i.jsx)(w.WooHeaderNavigationItem.Slot,{fillProps:{isEmbedded:t,query:r}}),(0,i.jsx)(M.Text,{className:(0,E.A)("woocommerce-layout__header-heading",{"woocommerce-layout__header-left-align":s}),as:"h1",children:(0,Le.decodeEntities)(d?(0,i.jsx)(w.WooHeaderPageTitle.Slot,{fillProps:{isEmbedded:t,query:r}}):Ve(n))}),a,(0,i.jsx)(w.WooHeaderItem.Slot,{fillProps:{isEmbedded:t,query:r}})]})]})},Qe=({sections:t,query:r})=>{const o=(0,W.getSetting)("siteTitle","");(0,e.useEffect)((()=>{const e=t.map((e=>Array.isArray(e)?e[1]:e)).reverse().join(" &lsaquo; "),r=(0,Le.decodeEntities)((0,v.sprintf)((0,v.__)("%1$s &lsaquo; %2$s &#8212; WooCommerce","woocommerce"),e,o));document.title!==r&&(document.title=r)}),[t,o]);const n=(0,h.isWCAdmin)()&&"homescreen"===(0,h.getScreenFromPath)()&&!r.task,{isLoading:a,launchYourStoreEnabled:s}=Y({enabled:n}),c=n&&s&&!a,l=(0,h.isWCAdmin)()&&"/analytics/overview"===(0,h.getPath)(),u=Boolean((0,J.EM)("setup"));return(0,i.jsxs)(Be,{isEmbedded:!1,sections:t,query:r,showReminderBar:u,leftAlign:!c,children:[c&&(0,i.jsx)(G,{}),l&&(0,i.jsx)(Re.Tv,{bannerType:Re.mg,eventContext:"analytics-overview-header"})]})},He=()=>{const e=(0,M.useSlot)(w.WC_FOOTER_SLOT_NAME),t=Boolean(e?.fills?.length),{atBottom:r}=Ue();return t?(0,i.jsx)("div",{className:(0,E.A)("woocommerce-layout__footer",{"at-bottom":r}),children:(0,i.jsx)(w.WooFooterItem.Slot,{})}):null};var Ge=Object.defineProperty,Ye={};((e,t)=>{for(var r in t)Ge(e,r,{get:t[r],enumerable:!0})})(Ye,{assign:()=>St,colors:()=>xt,createStringInterpolator:()=>wt,skipAnimation:()=>jt,to:()=>vt,willAdvance:()=>Mt});var We=lt(),$e=e=>at(e,We),qe=lt();$e.write=e=>at(e,qe);var Ze=lt();$e.onStart=e=>at(e,Ze);var Je=lt();$e.onFrame=e=>at(e,Je);var Ke=lt();$e.onFinish=e=>at(e,Ke);var Xe=[];$e.setTimeout=(e,t)=>{const r=$e.now()+t,o=()=>{const e=Xe.findIndex((e=>e.cancel==o));~e&&Xe.splice(e,1),ot-=~e?1:0},n={time:r,handler:e,cancel:o};return Xe.splice(et(r),0,n),ot+=1,st(),n};var et=e=>~(~Xe.findIndex((t=>t.time>e))||~Xe.length);$e.cancel=e=>{Ze.delete(e),Je.delete(e),Ke.delete(e),We.delete(e),qe.delete(e)},$e.sync=e=>{nt=!0,$e.batchedUpdates(e),nt=!1},$e.throttle=e=>{let t;function r(){try{e(...t)}finally{t=null}}function o(...e){t=e,$e.onStart(r)}return o.handler=e,o.cancel=()=>{Ze.delete(r),t=null},o};var tt="undefined"!=typeof window?window.requestAnimationFrame:()=>{};$e.use=e=>tt=e,$e.now="undefined"!=typeof performance?()=>performance.now():Date.now,$e.batchedUpdates=e=>e(),$e.catch=console.error,$e.frameLoop="always",$e.advance=()=>{"demand"!==$e.frameLoop?console.warn("Cannot call the manual advancement of rafz whilst frameLoop is not set as demand"):ct()};var rt=-1,ot=0,nt=!1;function at(e,t){nt?(t.delete(e),e(0)):(t.add(e),st())}function st(){rt<0&&(rt=0,"demand"!==$e.frameLoop&&tt(it))}function it(){~rt&&(tt(it),$e.batchedUpdates(ct))}function ct(){const e=rt;rt=$e.now();const t=et(rt);t&&(ut(Xe.splice(0,t),(e=>e.handler())),ot-=t),ot?(Ze.flush(),We.flush(e?Math.min(64,rt-e):16.667),Je.flush(),qe.flush(),Ke.flush()):rt=-1}function lt(){let e=new Set,t=e;return{add(r){ot+=t!=e||e.has(r)?0:1,e.add(r)},delete:r=>(ot-=t==e&&e.has(r)?1:0,e.delete(r)),flush(r){t.size&&(e=new Set,ot-=t.size,ut(t,(t=>t(r)&&e.add(t))),ot+=e.size,t=e)}}}function ut(e,t){e.forEach((e=>{try{t(e)}catch(e){$e.catch(e)}}))}function dt(){}var pt={arr:Array.isArray,obj:e=>!!e&&"Object"===e.constructor.name,fun:e=>"function"==typeof e,str:e=>"string"==typeof e,num:e=>"number"==typeof e,und:e=>void 0===e};function mt(e,t){if(pt.arr(e)){if(!pt.arr(t)||e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}return e===t}var ht=(e,t)=>e.forEach(t);function ft(e,t,r){if(pt.arr(e))for(let o=0;o<e.length;o++)t.call(r,e[o],`${o}`);else for(const o in e)e.hasOwnProperty(o)&&t.call(r,e[o],o)}var gt=e=>pt.und(e)?[]:pt.arr(e)?e:[e];function yt(e,t){if(e.size){const r=Array.from(e);e.clear(),ht(r,t)}}var wt,vt,bt=(e,...t)=>yt(e,(e=>e(...t))),_t=()=>"undefined"==typeof window||!window.navigator||/ServerSideRendering|^Deno\//.test(window.navigator.userAgent),xt=null,jt=!1,Mt=dt,St=e=>{e.to&&(vt=e.to),e.now&&($e.now=e.now),void 0!==e.colors&&(xt=e.colors),null!=e.skipAnimation&&(jt=e.skipAnimation),e.createStringInterpolator&&(wt=e.createStringInterpolator),e.requestAnimationFrame&&$e.use(e.requestAnimationFrame),e.batchedUpdates&&($e.batchedUpdates=e.batchedUpdates),e.willAdvance&&(Mt=e.willAdvance),e.frameLoop&&($e.frameLoop=e.frameLoop)},At=new Set,Ct=[],kt=[],Nt=0,Et={get idle(){return!At.size&&!Ct.length},start(e){Nt>e.priority?(At.add(e),$e.onStart(It)):(Tt(e),$e(Ot))},advance:Ot,sort(e){if(Nt)$e.onFrame((()=>Et.sort(e)));else{const t=Ct.indexOf(e);~t&&(Ct.splice(t,1),Pt(e))}},clear(){Ct=[],At.clear()}};function It(){At.forEach(Tt),At.clear(),$e(Ot)}function Tt(e){Ct.includes(e)||Pt(e)}function Pt(e){Ct.splice(function(t){const r=t.findIndex((t=>t.priority>e.priority));return r<0?t.length:r}(Ct),0,e)}function Ot(e){const t=kt;for(let r=0;r<Ct.length;r++){const o=Ct[r];Nt=o.priority,o.idle||(Mt(o),o.advance(e),o.idle||t.push(o))}return Nt=0,(kt=Ct).length=0,(Ct=t).length>0}var Dt="[-+]?\\d*\\.?\\d+",Lt=Dt+"%";function Rt(...e){return"\\(\\s*("+e.join(")\\s*,\\s*(")+")\\s*\\)"}var zt=new RegExp("rgb"+Rt(Dt,Dt,Dt)),Ft=new RegExp("rgba"+Rt(Dt,Dt,Dt,Dt)),Ut=new RegExp("hsl"+Rt(Dt,Lt,Lt)),Vt=new RegExp("hsla"+Rt(Dt,Lt,Lt,Dt)),Bt=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,Qt=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,Ht=/^#([0-9a-fA-F]{6})$/,Gt=/^#([0-9a-fA-F]{8})$/;function Yt(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+6*(t-e)*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function Wt(e,t,r){const o=r<.5?r*(1+t):r+t-r*t,n=2*r-o,a=Yt(n,o,e+1/3),s=Yt(n,o,e),i=Yt(n,o,e-1/3);return Math.round(255*a)<<24|Math.round(255*s)<<16|Math.round(255*i)<<8}function $t(e){const t=parseInt(e,10);return t<0?0:t>255?255:t}function qt(e){return(parseFloat(e)%360+360)%360/360}function Zt(e){const t=parseFloat(e);return t<0?0:t>1?255:Math.round(255*t)}function Jt(e){const t=parseFloat(e);return t<0?0:t>100?1:t/100}function Kt(e){let t=function(e){let t;return"number"==typeof e?e>>>0===e&&e>=0&&e<=4294967295?e:null:(t=Ht.exec(e))?parseInt(t[1]+"ff",16)>>>0:xt&&void 0!==xt[e]?xt[e]:(t=zt.exec(e))?($t(t[1])<<24|$t(t[2])<<16|$t(t[3])<<8|255)>>>0:(t=Ft.exec(e))?($t(t[1])<<24|$t(t[2])<<16|$t(t[3])<<8|Zt(t[4]))>>>0:(t=Bt.exec(e))?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+"ff",16)>>>0:(t=Gt.exec(e))?parseInt(t[1],16)>>>0:(t=Qt.exec(e))?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+t[4]+t[4],16)>>>0:(t=Ut.exec(e))?(255|Wt(qt(t[1]),Jt(t[2]),Jt(t[3])))>>>0:(t=Vt.exec(e))?(Wt(qt(t[1]),Jt(t[2]),Jt(t[3]))|Zt(t[4]))>>>0:null}(e);return null===t?e:(t=t||0,`rgba(${(4278190080&t)>>>24}, ${(16711680&t)>>>16}, ${(65280&t)>>>8}, ${(255&t)/255})`)}var Xt=(e,t,r)=>{if(pt.fun(e))return e;if(pt.arr(e))return Xt({range:e,output:t,extrapolate:r});if(pt.str(e.output[0]))return wt(e);const o=e,n=o.output,a=o.range||[0,1],s=o.extrapolateLeft||o.extrapolate||"extend",i=o.extrapolateRight||o.extrapolate||"extend",c=o.easing||(e=>e);return e=>{const t=function(e,t){for(var r=1;r<t.length-1&&!(t[r]>=e);++r);return r-1}(e,a);return function(e,t,r,o,n,a,s,i,c){let l=c?c(e):e;if(l<t){if("identity"===s)return l;"clamp"===s&&(l=t)}if(l>r){if("identity"===i)return l;"clamp"===i&&(l=r)}return o===n?o:t===r?e<=t?o:n:(t===-1/0?l=-l:r===1/0?l-=t:l=(l-t)/(r-t),l=a(l),o===-1/0?l=-l:n===1/0?l+=o:l=l*(n-o)+o,l)}(e,a[t],a[t+1],n[t],n[t+1],c,s,i,o.map)}},er=1.70158,tr=1.525*er,rr=er+1,or=2*Math.PI/3,nr=2*Math.PI/4.5,ar=e=>{const t=7.5625,r=2.75;return e<1/r?t*e*e:e<2/r?t*(e-=1.5/r)*e+.75:e<2.5/r?t*(e-=2.25/r)*e+.9375:t*(e-=2.625/r)*e+.984375},sr={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>1-(1-e)*(1-e),easeInOutQuad:e=>e<.5?2*e*e:1-Math.pow(-2*e+2,2)/2,easeInCubic:e=>e*e*e,easeOutCubic:e=>1-Math.pow(1-e,3),easeInOutCubic:e=>e<.5?4*e*e*e:1-Math.pow(-2*e+2,3)/2,easeInQuart:e=>e*e*e*e,easeOutQuart:e=>1-Math.pow(1-e,4),easeInOutQuart:e=>e<.5?8*e*e*e*e:1-Math.pow(-2*e+2,4)/2,easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>1-Math.pow(1-e,5),easeInOutQuint:e=>e<.5?16*e*e*e*e*e:1-Math.pow(-2*e+2,5)/2,easeInSine:e=>1-Math.cos(e*Math.PI/2),easeOutSine:e=>Math.sin(e*Math.PI/2),easeInOutSine:e=>-(Math.cos(Math.PI*e)-1)/2,easeInExpo:e=>0===e?0:Math.pow(2,10*e-10),easeOutExpo:e=>1===e?1:1-Math.pow(2,-10*e),easeInOutExpo:e=>0===e?0:1===e?1:e<.5?Math.pow(2,20*e-10)/2:(2-Math.pow(2,-20*e+10))/2,easeInCirc:e=>1-Math.sqrt(1-Math.pow(e,2)),easeOutCirc:e=>Math.sqrt(1-Math.pow(e-1,2)),easeInOutCirc:e=>e<.5?(1-Math.sqrt(1-Math.pow(2*e,2)))/2:(Math.sqrt(1-Math.pow(-2*e+2,2))+1)/2,easeInBack:e=>rr*e*e*e-er*e*e,easeOutBack:e=>1+rr*Math.pow(e-1,3)+er*Math.pow(e-1,2),easeInOutBack:e=>e<.5?Math.pow(2*e,2)*(7.189819*e-tr)/2:(Math.pow(2*e-2,2)*((tr+1)*(2*e-2)+tr)+2)/2,easeInElastic:e=>0===e?0:1===e?1:-Math.pow(2,10*e-10)*Math.sin((10*e-10.75)*or),easeOutElastic:e=>0===e?0:1===e?1:Math.pow(2,-10*e)*Math.sin((10*e-.75)*or)+1,easeInOutElastic:e=>0===e?0:1===e?1:e<.5?-Math.pow(2,20*e-10)*Math.sin((20*e-11.125)*nr)/2:Math.pow(2,-20*e+10)*Math.sin((20*e-11.125)*nr)/2+1,easeInBounce:e=>1-ar(1-e),easeOutBounce:ar,easeInOutBounce:e=>e<.5?(1-ar(1-2*e))/2:(1+ar(2*e-1))/2,steps:(e,t="end")=>r=>{const o=(r="end"===t?Math.min(r,.999):Math.max(r,.001))*e;return n=("end"===t?Math.floor(o):Math.ceil(o))/e,Math.min(Math.max(n,0),1);var n}},ir=Symbol.for("FluidValue.get"),cr=Symbol.for("FluidValue.observers"),lr=e=>Boolean(e&&e[ir]),ur=e=>e&&e[ir]?e[ir]():e,dr=e=>e[cr]||null;function pr(e,t){const r=e[cr];r&&r.forEach((e=>{!function(e,t){e.eventObserved?e.eventObserved(t):e(t)}(e,t)}))}var mr=class{constructor(e){if(!e&&!(e=this.get))throw Error("Unknown getter");hr(this,e)}},hr=(e,t)=>wr(e,ir,t);function fr(e,t){if(e[ir]){let r=e[cr];r||wr(e,cr,r=new Set),r.has(t)||(r.add(t),e.observerAdded&&e.observerAdded(r.size,t))}return t}function gr(e,t){const r=e[cr];if(r&&r.has(t)){const o=r.size-1;o?r.delete(t):e[cr]=null,e.observerRemoved&&e.observerRemoved(o,t)}}var yr,wr=(e,t,r)=>Object.defineProperty(e,t,{value:r,writable:!0,configurable:!0}),vr=/[+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,br=/(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d\.]+%?\))/gi,_r=new RegExp(`(${vr.source})(%|[a-z]+)`,"i"),xr=/rgba\(([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+)\)/gi,jr=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/,Mr=e=>{const[t,r]=Sr(e);if(!t||_t())return e;const o=window.getComputedStyle(document.documentElement).getPropertyValue(t);if(o)return o.trim();if(r&&r.startsWith("--")){return window.getComputedStyle(document.documentElement).getPropertyValue(r)||e}return r&&jr.test(r)?Mr(r):r||e},Sr=e=>{const t=jr.exec(e);if(!t)return[,];const[,r,o]=t;return[r,o]},Ar=(e,t,r,o,n)=>`rgba(${Math.round(t)}, ${Math.round(r)}, ${Math.round(o)}, ${n})`,Cr=e=>{yr||(yr=xt?new RegExp(`(${Object.keys(xt).join("|")})(?!\\w)`,"g"):/^\b$/);const t=e.output.map((e=>ur(e).replace(jr,Mr).replace(br,Kt).replace(yr,Kt))),r=t.map((e=>e.match(vr).map(Number))),o=r[0].map(((e,t)=>r.map((e=>{if(!(t in e))throw Error('The arity of each "output" value must be equal');return e[t]})))).map((t=>Xt({...e,output:t})));return e=>{const r=!_r.test(t[0])&&t.find((e=>_r.test(e)))?.replace(vr,"");let n=0;return t[0].replace(vr,(()=>`${o[n++](e)}${r||""}`)).replace(xr,Ar)}},kr="react-spring: ",Nr=e=>{const t=e;let r=!1;if("function"!=typeof t)throw new TypeError(`${kr}once requires a function parameter`);return(...e)=>{r||(t(...e),r=!0)}},Er=Nr(console.warn),Ir=Nr(console.warn);function Tr(e){return pt.str(e)&&("#"==e[0]||/\d/.test(e)||!_t()&&jr.test(e)||e in(xt||{}))}var Pr=_t()?H.useEffect:H.useLayoutEffect;function Or(){const e=(0,H.useState)()[1],t=(()=>{const e=(0,H.useRef)(!1);return Pr((()=>(e.current=!0,()=>{e.current=!1})),[]),e})();return()=>{t.current&&e(Math.random())}}var Dr=e=>(0,H.useEffect)(e,Lr),Lr=[],Rr=Symbol.for("Animated:node"),zr=e=>e&&e[Rr],Fr=(e,t)=>{return r=e,o=Rr,n=t,Object.defineProperty(r,o,{value:n,writable:!0,configurable:!0});var r,o,n},Ur=e=>e&&e[Rr]&&e[Rr].getPayload(),Vr=class{constructor(){Fr(this,this)}getPayload(){return this.payload||[]}},Br=class extends Vr{constructor(e){super(),this._value=e,this.done=!0,this.durationProgress=0,pt.num(this._value)&&(this.lastPosition=this._value)}static create(e){return new Br(e)}getPayload(){return[this]}getValue(){return this._value}setValue(e,t){return pt.num(e)&&(this.lastPosition=e,t&&(e=Math.round(e/t)*t,this.done&&(this.lastPosition=e))),this._value!==e&&(this._value=e,!0)}reset(){const{done:e}=this;this.done=!1,pt.num(this._value)&&(this.elapsedTime=0,this.durationProgress=0,this.lastPosition=this._value,e&&(this.lastVelocity=null),this.v0=null)}},Qr=class extends Br{constructor(e){super(0),this._string=null,this._toString=Xt({output:[e,e]})}static create(e){return new Qr(e)}getValue(){const e=this._string;return null==e?this._string=this._toString(this._value):e}setValue(e){if(pt.str(e)){if(e==this._string)return!1;this._string=e,this._value=1}else{if(!super.setValue(e))return!1;this._string=null}return!0}reset(e){e&&(this._toString=Xt({output:[this.getValue(),e]})),this._value=0,super.reset()}},Hr={dependencies:null},Gr=class extends Vr{constructor(e){super(),this.source=e,this.setValue(e)}getValue(e){const t={};return ft(this.source,((r,o)=>{var n;(n=r)&&n[Rr]===n?t[o]=r.getValue(e):lr(r)?t[o]=ur(r):e||(t[o]=r)})),t}setValue(e){this.source=e,this.payload=this._makePayload(e)}reset(){this.payload&&ht(this.payload,(e=>e.reset()))}_makePayload(e){if(e){const t=new Set;return ft(e,this._addToPayload,t),Array.from(t)}}_addToPayload(e){Hr.dependencies&&lr(e)&&Hr.dependencies.add(e);const t=Ur(e);t&&ht(t,(e=>this.add(e)))}},Yr=class extends Gr{constructor(e){super(e)}static create(e){return new Yr(e)}getValue(){return this.source.map((e=>e.getValue()))}setValue(e){const t=this.getPayload();return e.length==t.length?t.map(((t,r)=>t.setValue(e[r]))).some(Boolean):(super.setValue(e.map(Wr)),!0)}};function Wr(e){return(Tr(e)?Qr:Br).create(e)}function $r(e){const t=zr(e);return t?t.constructor:pt.arr(e)?Yr:Tr(e)?Qr:Br}var qr=(e,t)=>{const r=!pt.fun(e)||e.prototype&&e.prototype.isReactComponent;return(0,H.forwardRef)(((o,n)=>{const a=(0,H.useRef)(null),s=r&&(0,H.useCallback)((e=>{a.current=function(e,t){return e&&(pt.fun(e)?e(t):e.current=t),t}(n,e)}),[n]),[i,c]=function(e,t){const r=new Set;return Hr.dependencies=r,e.style&&(e={...e,style:t.createAnimatedStyle(e.style)}),e=new Gr(e),Hr.dependencies=null,[e,r]}(o,t),l=Or(),u=()=>{const e=a.current;r&&!e||!1===(!!e&&t.applyAnimatedValues(e,i.getValue(!0)))&&l()},d=new Zr(u,c),p=(0,H.useRef)();Pr((()=>(p.current=d,ht(c,(e=>fr(e,d))),()=>{p.current&&(ht(p.current.deps,(e=>gr(e,p.current))),$e.cancel(p.current.update))}))),(0,H.useEffect)(u,[]),Dr((()=>()=>{const e=p.current;ht(e.deps,(t=>gr(t,e)))}));const m=t.getComponentProps(i.getValue());return H.createElement(e,{...m,ref:s})}))},Zr=class{constructor(e,t){this.update=e,this.deps=t}eventObserved(e){"change"==e.type&&$e.write(this.update)}},Jr=Symbol.for("AnimatedComponent"),Kr=e=>pt.str(e)?e:e&&pt.str(e.displayName)?e.displayName:pt.fun(e)&&e.name||null;function Xr(e,...t){return pt.fun(e)?e(...t):e}var eo=(e,t)=>!0===e||!!(t&&e&&(pt.fun(e)?e(t):gt(e).includes(t))),to=(e,t)=>pt.obj(e)?t&&e[t]:e,ro=(e,t)=>!0===e.default?e[t]:e.default?e.default[t]:void 0,oo=e=>e,no=(e,t=oo)=>{let r=ao;e.default&&!0!==e.default&&(e=e.default,r=Object.keys(e));const o={};for(const n of r){const r=t(e[n],n);pt.und(r)||(o[n]=r)}return o},ao=["config","onProps","onStart","onChange","onPause","onResume","onRest"],so={config:1,from:1,to:1,ref:1,loop:1,reset:1,pause:1,cancel:1,reverse:1,immediate:1,default:1,delay:1,onProps:1,onStart:1,onChange:1,onPause:1,onResume:1,onRest:1,onResolve:1,items:1,trail:1,sort:1,expires:1,initial:1,enter:1,update:1,leave:1,children:1,onDestroyed:1,keys:1,callId:1,parentId:1};function io(e){const t=function(e){const t={};let r=0;if(ft(e,((e,o)=>{so[o]||(t[o]=e,r++)})),r)return t}(e);if(t){const r={to:t};return ft(e,((e,o)=>o in t||(r[o]=e))),r}return{...e}}function co(e){return e=ur(e),pt.arr(e)?e.map(co):Tr(e)?Ye.createStringInterpolator({range:[0,1],output:[e,e]})(1):e}function lo(e){return pt.fun(e)||pt.arr(e)&&pt.obj(e[0])}function uo(e,t){e.ref?.delete(e),t?.delete(e)}var po={tension:170,friction:26,mass:1,damping:1,easing:sr.linear,clamp:!1},mo=class{constructor(){this.velocity=0,Object.assign(this,po)}};function ho(e,t){if(pt.und(t.decay)){const r=!pt.und(t.tension)||!pt.und(t.friction);!r&&pt.und(t.frequency)&&pt.und(t.damping)&&pt.und(t.mass)||(e.duration=void 0,e.decay=void 0),r&&(e.frequency=void 0)}else e.duration=void 0}var fo=[],go=class{constructor(){this.changed=!1,this.values=fo,this.toValues=null,this.fromValues=fo,this.config=new mo,this.immediate=!1}};function yo(e,{key:t,props:r,defaultProps:o,state:n,actions:a}){return new Promise(((s,i)=>{let c,l,u=eo(r.cancel??o?.cancel,t);if(u)m();else{pt.und(r.pause)||(n.paused=eo(r.pause,t));let e=o?.pause;!0!==e&&(e=n.paused||eo(e,t)),c=Xr(r.delay||0,t),e?(n.resumeQueue.add(p),a.pause()):(a.resume(),p())}function d(){n.resumeQueue.add(p),n.timeouts.delete(l),l.cancel(),c=l.time-$e.now()}function p(){c>0&&!Ye.skipAnimation?(n.delayed=!0,l=$e.setTimeout(m,c),n.pauseQueue.add(d),n.timeouts.add(l)):m()}function m(){n.delayed&&(n.delayed=!1),n.pauseQueue.delete(d),n.timeouts.delete(l),e<=(n.cancelId||0)&&(u=!0);try{a.start({...r,callId:e,cancel:u},s)}catch(e){i(e)}}}))}var wo=(e,t)=>1==t.length?t[0]:t.some((e=>e.cancelled))?_o(e.get()):t.every((e=>e.noop))?vo(e.get()):bo(e.get(),t.every((e=>e.finished))),vo=e=>({value:e,noop:!0,finished:!0,cancelled:!1}),bo=(e,t,r=!1)=>({value:e,finished:t,cancelled:r}),_o=e=>({value:e,cancelled:!0,finished:!1});function xo(e,t,r,o){const{callId:n,parentId:a,onRest:s}=t,{asyncTo:i,promise:c}=r;return a||e!==i||t.reset?r.promise=(async()=>{r.asyncId=n,r.asyncTo=e;const l=no(t,((e,t)=>"onRest"===t?void 0:e));let u,d;const p=new Promise(((e,t)=>(u=e,d=t))),m=e=>{const t=n<=(r.cancelId||0)&&_o(o)||n!==r.asyncId&&bo(o,!1);if(t)throw e.result=t,d(e),e},h=(e,t)=>{const a=new Mo,s=new So;return(async()=>{if(Ye.skipAnimation)throw jo(r),s.result=bo(o,!1),d(s),s;m(a);const i=pt.obj(e)?{...e}:{...t,to:e};i.parentId=n,ft(l,((e,t)=>{pt.und(i[t])&&(i[t]=e)}));const c=await o.start(i);return m(a),r.paused&&await new Promise((e=>{r.resumeQueue.add(e)})),c})()};let f;if(Ye.skipAnimation)return jo(r),bo(o,!1);try{let t;t=pt.arr(e)?(async e=>{for(const t of e)await h(t)})(e):Promise.resolve(e(h,o.stop.bind(o))),await Promise.all([t.then(u),p]),f=bo(o.get(),!0,!1)}catch(e){if(e instanceof Mo)f=e.result;else{if(!(e instanceof So))throw e;f=e.result}}finally{n==r.asyncId&&(r.asyncId=a,r.asyncTo=a?i:void 0,r.promise=a?c:void 0)}return pt.fun(s)&&$e.batchedUpdates((()=>{s(f,o,o.item)})),f})():c}function jo(e,t){yt(e.timeouts,(e=>e.cancel())),e.pauseQueue.clear(),e.resumeQueue.clear(),e.asyncId=e.asyncTo=e.promise=void 0,t&&(e.cancelId=t)}var Mo=class extends Error{constructor(){super("An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.")}},So=class extends Error{constructor(){super("SkipAnimationSignal")}},Ao=e=>e instanceof ko,Co=1,ko=class extends mr{constructor(){super(...arguments),this.id=Co++,this._priority=0}get priority(){return this._priority}set priority(e){this._priority!=e&&(this._priority=e,this._onPriorityChange(e))}get(){const e=zr(this);return e&&e.getValue()}to(...e){return Ye.to(this,e)}interpolate(...e){return Er(`${kr}The "interpolate" function is deprecated in v9 (use "to" instead)`),Ye.to(this,e)}toJSON(){return this.get()}observerAdded(e){1==e&&this._attach()}observerRemoved(e){0==e&&this._detach()}_attach(){}_detach(){}_onChange(e,t=!1){pr(this,{type:"change",parent:this,value:e,idle:t})}_onPriorityChange(e){this.idle||Et.sort(this),pr(this,{type:"priority",parent:this,priority:e})}},No=Symbol.for("SpringPhase"),Eo=e=>(1&e[No])>0,Io=e=>(2&e[No])>0,To=e=>(4&e[No])>0,Po=(e,t)=>t?e[No]|=3:e[No]&=-3,Oo=(e,t)=>t?e[No]|=4:e[No]&=-5,Do=class extends ko{constructor(e,t){if(super(),this.animation=new go,this.defaultProps={},this._state={paused:!1,delayed:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set},this._pendingCalls=new Set,this._lastCallId=0,this._lastToId=0,this._memoizedDuration=0,!pt.und(e)||!pt.und(t)){const r=pt.obj(e)?{...e}:{...t,from:e};pt.und(r.default)&&(r.default=!0),this.start(r)}}get idle(){return!(Io(this)||this._state.asyncTo)||To(this)}get goal(){return ur(this.animation.to)}get velocity(){const e=zr(this);return e instanceof Br?e.lastVelocity||0:e.getPayload().map((e=>e.lastVelocity||0))}get hasAnimated(){return Eo(this)}get isAnimating(){return Io(this)}get isPaused(){return To(this)}get isDelayed(){return this._state.delayed}advance(e){let t=!0,r=!1;const o=this.animation;let{toValues:n}=o;const{config:a}=o,s=Ur(o.to);!s&&lr(o.to)&&(n=gt(ur(o.to))),o.values.forEach(((i,c)=>{if(i.done)return;const l=i.constructor==Qr?1:s?s[c].lastPosition:n[c];let u=o.immediate,d=l;if(!u){if(d=i.lastPosition,a.tension<=0)return void(i.done=!0);let t=i.elapsedTime+=e;const r=o.fromValues[c],n=null!=i.v0?i.v0:i.v0=pt.arr(a.velocity)?a.velocity[c]:a.velocity;let s;const p=a.precision||(r==l?.005:Math.min(1,.001*Math.abs(l-r)));if(pt.und(a.duration))if(a.decay){const e=!0===a.decay?.998:a.decay,o=Math.exp(-(1-e)*t);d=r+n/(1-e)*(1-o),u=Math.abs(i.lastPosition-d)<=p,s=n*o}else{s=null==i.lastVelocity?n:i.lastVelocity;const t=a.restVelocity||p/10,o=a.clamp?0:a.bounce,c=!pt.und(o),m=r==l?i.v0>0:r<l;let h,f=!1;const g=1,y=Math.ceil(e/g);for(let e=0;e<y&&(h=Math.abs(s)>t,h||(u=Math.abs(l-d)<=p,!u));++e)c&&(f=d==l||d>l==m,f&&(s=-s*o,d=l)),s+=(1e-6*-a.tension*(d-l)+.001*-a.friction*s)/a.mass*g,d+=s*g}else{let o=1;a.duration>0&&(this._memoizedDuration!==a.duration&&(this._memoizedDuration=a.duration,i.durationProgress>0&&(i.elapsedTime=a.duration*i.durationProgress,t=i.elapsedTime+=e)),o=(a.progress||0)+t/this._memoizedDuration,o=o>1?1:o<0?0:o,i.durationProgress=o),d=r+a.easing(o)*(l-r),s=(d-i.lastPosition)/e,u=1==o}i.lastVelocity=s,Number.isNaN(d)&&(console.warn("Got NaN while animating:",this),u=!0)}s&&!s[c].done&&(u=!1),u?i.done=!0:t=!1,i.setValue(d,a.round)&&(r=!0)}));const i=zr(this),c=i.getValue();if(t){const e=ur(o.to);c===e&&!r||a.decay?r&&a.decay&&this._onChange(c):(i.setValue(e),this._onChange(e)),this._stop()}else r&&this._onChange(c)}set(e){return $e.batchedUpdates((()=>{this._stop(),this._focus(e),this._set(e)})),this}pause(){this._update({pause:!0})}resume(){this._update({pause:!1})}finish(){if(Io(this)){const{to:e,config:t}=this.animation;$e.batchedUpdates((()=>{this._onStart(),t.decay||this._set(e,!1),this._stop()}))}return this}update(e){return(this.queue||(this.queue=[])).push(e),this}start(e,t){let r;return pt.und(e)?(r=this.queue||[],this.queue=[]):r=[pt.obj(e)?e:{...t,to:e}],Promise.all(r.map((e=>this._update(e)))).then((e=>wo(this,e)))}stop(e){const{to:t}=this.animation;return this._focus(this.get()),jo(this._state,e&&this._lastCallId),$e.batchedUpdates((()=>this._stop(t,e))),this}reset(){this._update({reset:!0})}eventObserved(e){"change"==e.type?this._start():"priority"==e.type&&(this.priority=e.priority+1)}_prepareNode(e){const t=this.key||"";let{to:r,from:o}=e;r=pt.obj(r)?r[t]:r,(null==r||lo(r))&&(r=void 0),o=pt.obj(o)?o[t]:o,null==o&&(o=void 0);const n={to:r,from:o};return Eo(this)||(e.reverse&&([r,o]=[o,r]),o=ur(o),pt.und(o)?zr(this)||this._set(r):this._set(o)),n}_update({...e},t){const{key:r,defaultProps:o}=this;e.default&&Object.assign(o,no(e,((e,t)=>/^on/.test(t)?to(e,r):e))),Vo(this,e,"onProps"),Bo(this,"onProps",e,this);const n=this._prepareNode(e);if(Object.isFrozen(this))throw Error("Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?");const a=this._state;return yo(++this._lastCallId,{key:r,props:e,defaultProps:o,state:a,actions:{pause:()=>{To(this)||(Oo(this,!0),bt(a.pauseQueue),Bo(this,"onPause",bo(this,Lo(this,this.animation.to)),this))},resume:()=>{To(this)&&(Oo(this,!1),Io(this)&&this._resume(),bt(a.resumeQueue),Bo(this,"onResume",bo(this,Lo(this,this.animation.to)),this))},start:this._merge.bind(this,n)}}).then((r=>{if(e.loop&&r.finished&&(!t||!r.noop)){const t=Ro(e);if(t)return this._update(t,!0)}return r}))}_merge(e,t,r){if(t.cancel)return this.stop(!0),r(_o(this));const o=!pt.und(e.to),n=!pt.und(e.from);if(o||n){if(!(t.callId>this._lastToId))return r(_o(this));this._lastToId=t.callId}const{key:a,defaultProps:s,animation:i}=this,{to:c,from:l}=i;let{to:u=c,from:d=l}=e;!n||o||t.default&&!pt.und(u)||(u=d),t.reverse&&([u,d]=[d,u]);const p=!mt(d,l);p&&(i.from=d),d=ur(d);const m=!mt(u,c);m&&this._focus(u);const h=lo(t.to),{config:f}=i,{decay:g,velocity:y}=f;(o||n)&&(f.velocity=0),t.config&&!h&&function(e,t,r){r&&(ho(r={...r},t),t={...r,...t}),ho(e,t),Object.assign(e,t);for(const t in po)null==e[t]&&(e[t]=po[t]);let{frequency:o,damping:n}=e;const{mass:a}=e;pt.und(o)||(o<.01&&(o=.01),n<0&&(n=0),e.tension=Math.pow(2*Math.PI/o,2)*a,e.friction=4*Math.PI*n*a/o)}(f,Xr(t.config,a),t.config!==s.config?Xr(s.config,a):void 0);let w=zr(this);if(!w||pt.und(u))return r(bo(this,!0));const v=pt.und(t.reset)?n&&!t.default:!pt.und(d)&&eo(t.reset,a),b=v?d:this.get(),_=co(u),x=pt.num(_)||pt.arr(_)||Tr(_),j=!h&&(!x||eo(s.immediate||t.immediate,a));if(m){const e=$r(u);if(e!==w.constructor){if(!j)throw Error(`Cannot animate between ${w.constructor.name} and ${e.name}, as the "to" prop suggests`);w=this._set(_)}}const M=w.constructor;let S=lr(u),A=!1;if(!S){const e=v||!Eo(this)&&p;(m||e)&&(A=mt(co(b),_),S=!A),(mt(i.immediate,j)||j)&&mt(f.decay,g)&&mt(f.velocity,y)||(S=!0)}if(A&&Io(this)&&(i.changed&&!v?S=!0:S||this._stop(c)),!h&&((S||lr(c))&&(i.values=w.getPayload(),i.toValues=lr(u)?null:M==Qr?[1]:gt(_)),i.immediate!=j&&(i.immediate=j,j||v||this._set(c)),S)){const{onRest:e}=i;ht(Uo,(e=>Vo(this,t,e)));const o=bo(this,Lo(this,c));bt(this._pendingCalls,o),this._pendingCalls.add(r),i.changed&&$e.batchedUpdates((()=>{i.changed=!v,e?.(o,this),v?Xr(s.onRest,o):i.onStart?.(o,this)}))}v&&this._set(b),h?r(xo(t.to,t,this._state,this)):S?this._start():Io(this)&&!m?this._pendingCalls.add(r):r(vo(b))}_focus(e){const t=this.animation;e!==t.to&&(dr(this)&&this._detach(),t.to=e,dr(this)&&this._attach())}_attach(){let e=0;const{to:t}=this.animation;lr(t)&&(fr(t,this),Ao(t)&&(e=t.priority+1)),this.priority=e}_detach(){const{to:e}=this.animation;lr(e)&&gr(e,this)}_set(e,t=!0){const r=ur(e);if(!pt.und(r)){const e=zr(this);if(!e||!mt(r,e.getValue())){const o=$r(r);e&&e.constructor==o?e.setValue(r):Fr(this,o.create(r)),e&&$e.batchedUpdates((()=>{this._onChange(r,t)}))}}return zr(this)}_onStart(){const e=this.animation;e.changed||(e.changed=!0,Bo(this,"onStart",bo(this,Lo(this,e.to)),this))}_onChange(e,t){t||(this._onStart(),Xr(this.animation.onChange,e,this)),Xr(this.defaultProps.onChange,e,this),super._onChange(e,t)}_start(){const e=this.animation;zr(this).reset(ur(e.to)),e.immediate||(e.fromValues=e.values.map((e=>e.lastPosition))),Io(this)||(Po(this,!0),To(this)||this._resume())}_resume(){Ye.skipAnimation?this.finish():Et.start(this)}_stop(e,t){if(Io(this)){Po(this,!1);const r=this.animation;ht(r.values,(e=>{e.done=!0})),r.toValues&&(r.onChange=r.onPause=r.onResume=void 0),pr(this,{type:"idle",parent:this});const o=t?_o(this.get()):bo(this.get(),Lo(this,e??r.to));bt(this._pendingCalls,o),r.changed&&(r.changed=!1,Bo(this,"onRest",o,this))}}};function Lo(e,t){const r=co(t);return mt(co(e.get()),r)}function Ro(e,t=e.loop,r=e.to){const o=Xr(t);if(o){const n=!0!==o&&io(o),a=(n||e).reverse,s=!n||n.reset;return zo({...e,loop:t,default:!1,pause:void 0,to:!a||lo(r)?r:void 0,from:s?e.from:void 0,reset:s,...n})}}function zo(e){const{to:t,from:r}=e=io(e),o=new Set;return pt.obj(t)&&Fo(t,o),pt.obj(r)&&Fo(r,o),e.keys=o.size?Array.from(o):null,e}function Fo(e,t){ft(e,((e,r)=>null!=e&&t.add(r)))}var Uo=["onStart","onRest","onChange","onPause","onResume"];function Vo(e,t,r){e.animation[r]=t[r]!==ro(t,r)?to(t[r],e.key):void 0}function Bo(e,t,...r){e.animation[t]?.(...r),e.defaultProps[t]?.(...r)}var Qo=["onStart","onChange","onRest"],Ho=1,Go=class{constructor(e,t){this.id=Ho++,this.springs={},this.queue=[],this._lastAsyncId=0,this._active=new Set,this._changed=new Set,this._started=!1,this._state={paused:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set},this._events={onStart:new Map,onChange:new Map,onRest:new Map},this._onFrame=this._onFrame.bind(this),t&&(this._flush=t),e&&this.start({default:!0,...e})}get idle(){return!this._state.asyncTo&&Object.values(this.springs).every((e=>e.idle&&!e.isDelayed&&!e.isPaused))}get item(){return this._item}set item(e){this._item=e}get(){const e={};return this.each(((t,r)=>e[r]=t.get())),e}set(e){for(const t in e){const r=e[t];pt.und(r)||this.springs[t].set(r)}}update(e){return e&&this.queue.push(zo(e)),this}start(e){let{queue:t}=this;return e?t=gt(e).map(zo):this.queue=[],this._flush?this._flush(this,t):(Zo(this,t),function(e,t){return Promise.all(t.map((t=>Yo(e,t)))).then((t=>wo(e,t)))}(this,t))}stop(e,t){if(e!==!!e&&(t=e),t){const r=this.springs;ht(gt(t),(t=>r[t].stop(!!e)))}else jo(this._state,this._lastAsyncId),this.each((t=>t.stop(!!e)));return this}pause(e){if(pt.und(e))this.start({pause:!0});else{const t=this.springs;ht(gt(e),(e=>t[e].pause()))}return this}resume(e){if(pt.und(e))this.start({pause:!1});else{const t=this.springs;ht(gt(e),(e=>t[e].resume()))}return this}each(e){ft(this.springs,e)}_onFrame(){const{onStart:e,onChange:t,onRest:r}=this._events,o=this._active.size>0,n=this._changed.size>0;(o&&!this._started||n&&!this._started)&&(this._started=!0,yt(e,(([e,t])=>{t.value=this.get(),e(t,this,this._item)})));const a=!o&&this._started,s=n||a&&r.size?this.get():null;n&&t.size&&yt(t,(([e,t])=>{t.value=s,e(t,this,this._item)})),a&&(this._started=!1,yt(r,(([e,t])=>{t.value=s,e(t,this,this._item)})))}eventObserved(e){if("change"==e.type)this._changed.add(e.parent),e.idle||this._active.add(e.parent);else{if("idle"!=e.type)return;this._active.delete(e.parent)}$e.onFrame(this._onFrame)}};async function Yo(e,t,r){const{keys:o,to:n,from:a,loop:s,onRest:i,onResolve:c}=t,l=pt.obj(t.default)&&t.default;s&&(t.loop=!1),!1===n&&(t.to=null),!1===a&&(t.from=null);const u=pt.arr(n)||pt.fun(n)?n:void 0;u?(t.to=void 0,t.onRest=void 0,l&&(l.onRest=void 0)):ht(Qo,(r=>{const o=t[r];if(pt.fun(o)){const n=e._events[r];t[r]=({finished:e,cancelled:t})=>{const r=n.get(o);r?(e||(r.finished=!1),t&&(r.cancelled=!0)):n.set(o,{value:null,finished:e||!1,cancelled:t||!1})},l&&(l[r]=t[r])}}));const d=e._state;t.pause===!d.paused?(d.paused=t.pause,bt(t.pause?d.pauseQueue:d.resumeQueue)):d.paused&&(t.pause=!0);const p=(o||Object.keys(e.springs)).map((r=>e.springs[r].start(t))),m=!0===t.cancel||!0===ro(t,"cancel");(u||m&&d.asyncId)&&p.push(yo(++e._lastAsyncId,{props:t,state:d,actions:{pause:dt,resume:dt,start(t,r){m?(jo(d,e._lastAsyncId),r(_o(e))):(t.onRest=i,r(xo(u,t,d,e)))}}})),d.paused&&await new Promise((e=>{d.resumeQueue.add(e)}));const h=wo(e,await Promise.all(p));if(s&&h.finished&&(!r||!h.noop)){const r=Ro(t,s,n);if(r)return Zo(e,[r]),Yo(e,r,!0)}return c&&$e.batchedUpdates((()=>c(h,e,e.item))),h}function Wo(e,t){const r={...e.springs};return t&&ht(gt(t),(e=>{pt.und(e.keys)&&(e=zo(e)),pt.obj(e.to)||(e={...e,to:void 0}),qo(r,e,(e=>$o(e)))})),function(e,t){ft(t,((t,r)=>{e.springs[r]||(e.springs[r]=t,fr(t,e))}))}(e,r),r}function $o(e,t){const r=new Do;return r.key=e,t&&fr(r,t),r}function qo(e,t,r){t.keys&&ht(t.keys,(o=>{(e[o]||(e[o]=r(o)))._prepareNode(t)}))}function Zo(e,t){ht(t,(t=>{qo(e.springs,t,(t=>$o(t,e)))}))}var Jo,Ko,Xo=({children:e,...t})=>{const r=(0,H.useContext)(en),o=t.pause||!!r.pause,n=t.immediate||!!r.immediate;t=function(e,t){const[r]=(0,H.useState)((()=>({inputs:t,result:e()}))),o=(0,H.useRef)(),n=o.current;let a=n;return a?Boolean(t&&a.inputs&&function(e,t){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}(t,a.inputs))||(a={inputs:t,result:e()}):a=r,(0,H.useEffect)((()=>{o.current=a,n==r&&(r.inputs=r.result=void 0)}),[a]),a.result}((()=>({pause:o,immediate:n})),[o,n]);const{Provider:a}=en;return H.createElement(a,{value:t},e)},en=(Jo=Xo,Ko={},Object.assign(Jo,H.createContext(Ko)),Jo.Provider._context=Jo,Jo.Consumer._context=Jo,Jo);Xo.Provider=en.Provider,Xo.Consumer=en.Consumer;var tn=()=>{const e=[],t=function(t){Ir(`${kr}Directly calling start instead of using the api object is deprecated in v9 (use ".start" instead), this will be removed in later 0.X.0 versions`);const o=[];return ht(e,((e,n)=>{if(pt.und(t))o.push(e.start());else{const a=r(t,e,n);a&&o.push(e.start(a))}})),o};t.current=e,t.add=function(t){e.includes(t)||e.push(t)},t.delete=function(t){const r=e.indexOf(t);~r&&e.splice(r,1)},t.pause=function(){return ht(e,(e=>e.pause(...arguments))),this},t.resume=function(){return ht(e,(e=>e.resume(...arguments))),this},t.set=function(t){ht(e,((e,r)=>{const o=pt.fun(t)?t(r,e):t;o&&e.set(o)}))},t.start=function(t){const r=[];return ht(e,((e,o)=>{if(pt.und(t))r.push(e.start());else{const n=this._getProps(t,e,o);n&&r.push(e.start(n))}})),r},t.stop=function(){return ht(e,(e=>e.stop(...arguments))),this},t.update=function(t){return ht(e,((e,r)=>e.update(this._getProps(t,e,r)))),this};const r=function(e,t,r){return pt.fun(e)?e(r,t):e};return t._getProps=r,t};var rn=1,on=class extends ko{constructor(e,t){super(),this.source=e,this.idle=!0,this._active=new Set,this.calc=Xt(...t);const r=this._get(),o=$r(r);Fr(this,o.create(r))}advance(e){const t=this._get();mt(t,this.get())||(zr(this).setValue(t),this._onChange(t,this.idle)),!this.idle&&an(this._active)&&sn(this)}_get(){const e=pt.arr(this.source)?this.source.map(ur):gt(ur(this.source));return this.calc(...e)}_start(){this.idle&&!an(this._active)&&(this.idle=!1,ht(Ur(this),(e=>{e.done=!1})),Ye.skipAnimation?($e.batchedUpdates((()=>this.advance())),sn(this)):Et.start(this))}_attach(){let e=1;ht(gt(this.source),(t=>{lr(t)&&fr(t,this),Ao(t)&&(t.idle||this._active.add(t),e=Math.max(e,t.priority+1))})),this.priority=e,this._start()}_detach(){ht(gt(this.source),(e=>{lr(e)&&gr(e,this)})),this._active.clear(),sn(this)}eventObserved(e){"change"==e.type?e.idle?this.advance():(this._active.add(e.parent),this._start()):"idle"==e.type?this._active.delete(e.parent):"priority"==e.type&&(this.priority=gt(this.source).reduce(((e,t)=>Math.max(e,(Ao(t)?t.priority:0)+1)),0))}};function nn(e){return!1!==e.idle}function an(e){return!e.size||Array.from(e).every(nn)}function sn(e){e.idle||(e.idle=!0,ht(Ur(e),(e=>{e.done=!0})),pr(e,{type:"idle",parent:e}))}Ye.assign({createStringInterpolator:Cr,to:(e,t)=>new on(e,t)}),Et.advance;var cn=s(75795),ln=/^--/;function un(e,t){return null==t||"boolean"==typeof t||""===t?"":"number"!=typeof t||0===t||ln.test(e)||pn.hasOwnProperty(e)&&pn[e]?(""+t).trim():t+"px"}var dn={},pn={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},mn=["Webkit","Ms","Moz","O"];pn=Object.keys(pn).reduce(((e,t)=>(mn.forEach((r=>e[((e,t)=>e+t.charAt(0).toUpperCase()+t.substring(1))(r,t)]=e[t])),e)),pn);var hn=/^(matrix|translate|scale|rotate|skew)/,fn=/^(translate)/,gn=/^(rotate|skew)/,yn=(e,t)=>pt.num(e)&&0!==e?e+t:e,wn=(e,t)=>pt.arr(e)?e.every((e=>wn(e,t))):pt.num(e)?e===t:parseFloat(e)===t,vn=class extends Gr{constructor({x:e,y:t,z:r,...o}){const n=[],a=[];(e||t||r)&&(n.push([e||0,t||0,r||0]),a.push((e=>[`translate3d(${e.map((e=>yn(e,"px"))).join(",")})`,wn(e,0)]))),ft(o,((e,t)=>{if("transform"===t)n.push([e||""]),a.push((e=>[e,""===e]));else if(hn.test(t)){if(delete o[t],pt.und(e))return;const r=fn.test(t)?"px":gn.test(t)?"deg":"";n.push(gt(e)),a.push("rotate3d"===t?([e,t,o,n])=>[`rotate3d(${e},${t},${o},${yn(n,r)})`,wn(n,0)]:e=>[`${t}(${e.map((e=>yn(e,r))).join(",")})`,wn(e,t.startsWith("scale")?1:0)])}})),n.length&&(o.transform=new bn(n,a)),super(o)}},bn=class extends mr{constructor(e,t){super(),this.inputs=e,this.transforms=t,this._value=null}get(){return this._value||(this._value=this._get())}_get(){let e="",t=!0;return ht(this.inputs,((r,o)=>{const n=ur(r[0]),[a,s]=this.transforms[o](pt.arr(n)?n:r.map(ur));e+=" "+a,t=t&&s})),t?"none":e}observerAdded(e){1==e&&ht(this.inputs,(e=>ht(e,(e=>lr(e)&&fr(e,this)))))}observerRemoved(e){0==e&&ht(this.inputs,(e=>ht(e,(e=>lr(e)&&gr(e,this)))))}eventObserved(e){"change"==e.type&&(this._value=null),pr(this,e)}};Ye.assign({batchedUpdates:cn.unstable_batchedUpdates,createStringInterpolator:Cr,colors:{transparent:0,aliceblue:4042850303,antiquewhite:4209760255,aqua:16777215,aquamarine:2147472639,azure:4043309055,beige:4126530815,bisque:4293182719,black:255,blanchedalmond:4293643775,blue:65535,blueviolet:2318131967,brown:2771004159,burlywood:3736635391,burntsienna:3934150143,cadetblue:1604231423,chartreuse:2147418367,chocolate:3530104575,coral:4286533887,cornflowerblue:1687547391,cornsilk:4294499583,crimson:3692313855,cyan:16777215,darkblue:35839,darkcyan:9145343,darkgoldenrod:3095792639,darkgray:2846468607,darkgreen:6553855,darkgrey:2846468607,darkkhaki:3182914559,darkmagenta:2332068863,darkolivegreen:1433087999,darkorange:4287365375,darkorchid:2570243327,darkred:2332033279,darksalmon:3918953215,darkseagreen:2411499519,darkslateblue:1211993087,darkslategray:793726975,darkslategrey:793726975,darkturquoise:13554175,darkviolet:2483082239,deeppink:4279538687,deepskyblue:12582911,dimgray:1768516095,dimgrey:1768516095,dodgerblue:512819199,firebrick:2988581631,floralwhite:4294635775,forestgreen:579543807,fuchsia:4278255615,gainsboro:3705462015,ghostwhite:4177068031,gold:4292280575,goldenrod:3668254975,gray:2155905279,green:8388863,greenyellow:2919182335,grey:2155905279,honeydew:4043305215,hotpink:4285117695,indianred:3445382399,indigo:1258324735,ivory:4294963455,khaki:4041641215,lavender:3873897215,lavenderblush:4293981695,lawngreen:2096890111,lemonchiffon:4294626815,lightblue:2916673279,lightcoral:4034953471,lightcyan:3774873599,lightgoldenrodyellow:4210742015,lightgray:3553874943,lightgreen:2431553791,lightgrey:3553874943,lightpink:4290167295,lightsalmon:4288707327,lightseagreen:548580095,lightskyblue:2278488831,lightslategray:2005441023,lightslategrey:2005441023,lightsteelblue:2965692159,lightyellow:4294959359,lime:16711935,limegreen:852308735,linen:4210091775,magenta:4278255615,maroon:2147483903,mediumaquamarine:1724754687,mediumblue:52735,mediumorchid:3126187007,mediumpurple:2473647103,mediumseagreen:1018393087,mediumslateblue:2070474495,mediumspringgreen:16423679,mediumturquoise:1221709055,mediumvioletred:3340076543,midnightblue:421097727,mintcream:4127193855,mistyrose:4293190143,moccasin:4293178879,navajowhite:4292783615,navy:33023,oldlace:4260751103,olive:2155872511,olivedrab:1804477439,orange:4289003775,orangered:4282712319,orchid:3664828159,palegoldenrod:4008225535,palegreen:2566625535,paleturquoise:2951671551,palevioletred:3681588223,papayawhip:4293907967,peachpuff:4292524543,peru:3448061951,pink:4290825215,plum:3718307327,powderblue:2967529215,purple:2147516671,rebeccapurple:1714657791,red:4278190335,rosybrown:3163525119,royalblue:1097458175,saddlebrown:2336560127,salmon:4202722047,sandybrown:4104413439,seagreen:780883967,seashell:4294307583,sienna:2689740287,silver:3233857791,skyblue:2278484991,slateblue:1784335871,slategray:1887473919,slategrey:1887473919,snow:4294638335,springgreen:16744447,steelblue:1182971135,tan:3535047935,teal:8421631,thistle:3636451583,tomato:4284696575,turquoise:1088475391,violet:4001558271,wheat:4125012991,white:4294967295,whitesmoke:4126537215,yellow:4294902015,yellowgreen:2597139199}});var _n=((e,{applyAnimatedValues:t=()=>!1,createAnimatedStyle:r=e=>new Gr(e),getComponentProps:o=e=>e}={})=>{const n={applyAnimatedValues:t,createAnimatedStyle:r,getComponentProps:o},a=e=>{const t=Kr(e)||"Anonymous";return(e=pt.str(e)?a[e]||(a[e]=qr(e,n)):e[Jr]||(e[Jr]=qr(e,n))).displayName=`Animated(${t})`,e};return ft(e,((t,r)=>{pt.arr(e)&&(r=Kr(t)),a[r]=a(t)})),{animated:a}})(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],{applyAnimatedValues:function(e,t){if(!e.nodeType||!e.setAttribute)return!1;const r="filter"===e.nodeName||e.parentNode&&"filter"===e.parentNode.nodeName,{style:o,children:n,scrollTop:a,scrollLeft:s,viewBox:i,...c}=t,l=Object.values(c),u=Object.keys(c).map((t=>r||e.hasAttribute(t)?t:dn[t]||(dn[t]=t.replace(/([A-Z])/g,(e=>"-"+e.toLowerCase())))));void 0!==n&&(e.textContent=n);for(const t in o)if(o.hasOwnProperty(t)){const r=un(t,o[t]);ln.test(t)?e.style.setProperty(t,r):e.style[t]=r}u.forEach(((t,r)=>{e.setAttribute(t,l[r])})),void 0!==a&&(e.scrollTop=a),void 0!==s&&(e.scrollLeft=s),void 0!==i&&e.setAttribute("viewBox",i)},createAnimatedStyle:e=>new vn(e),getComponentProps:({scrollTop:e,scrollLeft:t,...r})=>r}),xn=_n.animated,jn=s(20195);const Mn=window.wp.warning;var Sn=s.n(Mn);const An=(0,e.forwardRef)((function({className:t,children:r,spokenMessage:o=r,politeness:n="polite",actions:a=[],onRemove:s=m.noop,icon:c=null,explicitDismiss:l=!1,onDismiss:u=null,__unstableHTML:d=!1},p){function h(e){e&&e.preventDefault&&e.preventDefault(),u(),s()}u=u||m.noop,function(t,r){const o="string"==typeof t?t:(0,e.renderToString)(t);(0,e.useEffect)((()=>{o&&(0,jn.speak)(o,r)}),[o,r])}(o,n),(0,e.useEffect)((()=>{const e=setTimeout((()=>{l||(u(),s())}),1e4);return()=>clearTimeout(e)}),[l,u,s]);const g=(0,E.A)(t,"components-snackbar",{"components-snackbar-explicit-dismiss":!!l});a&&a.length>1&&(!0===globalThis.SCRIPT_DEBUG&&Sn()("Snackbar can only have 1 action, use Notice if your message require many messages"),a=[a[0]]);const y=(0,E.A)("components-snackbar__content",{"components-snackbar__content-with-icon":!!c});return!0===d&&(r=(0,i.jsx)(e.RawHTML,{children:r})),(0,i.jsx)("div",{ref:p,className:g,onClick:l?m.noop:h,tabIndex:"0",role:l?"":"button",onKeyPress:l?m.noop:h,"aria-label":l?"":(0,v.__)("Dismiss this notice","woocommerce"),children:(0,i.jsxs)("div",{className:y,children:[c&&(0,i.jsx)("div",{className:"components-snackbar__icon",children:c}),r,a.map((({label:e,onClick:t,url:r},o)=>(0,i.jsx)(f.Button,{href:r,isTertiary:!0,onClick:e=>function(e,t){e.stopPropagation(),s(),t&&t(e)}(e,t),className:"components-snackbar__action",children:e},o))),l&&(0,i.jsx)("span",{role:"button","aria-label":"Dismiss this notice",tabIndex:"0",className:"components-snackbar__dismiss-button",onClick:h,onKeyPress:h,children:"✕"})]})})})),Cn=function({notices:t,className:r,children:o,onRemove:n=m.noop,onRemove2:a=m.noop}){const s=(0,l.useReducedMotion)(),[c]=(0,e.useState)((()=>new WeakMap)),u=function(e,t,r){const o=pt.fun(t)&&t,{reset:n,sort:a,trail:s=0,expires:i=!0,exitBeforeEnter:c=!1,onDestroyed:l,ref:u,config:d}=o?o():t,p=(0,H.useMemo)((()=>o||3==arguments.length?tn():void 0),[]),m=gt(e),h=[],f=(0,H.useRef)(null),g=n?null:f.current;Pr((()=>{f.current=h})),Dr((()=>(ht(h,(e=>{p?.add(e.ctrl),e.ctrl.ref=p})),()=>{ht(f.current,(e=>{e.expired&&clearTimeout(e.expirationId),uo(e.ctrl,p),e.ctrl.stop(!0)}))})));const y=function(e,{key:t,keys:r=t},o){if(null===r){const t=new Set;return e.map((e=>{const r=o&&o.find((r=>r.item===e&&"leave"!==r.phase&&!t.has(r)));return r?(t.add(r),r.key):rn++}))}return pt.und(r)?e:pt.fun(r)?e.map(r):gt(r)}(m,o?o():t,g),w=n&&f.current||[];Pr((()=>ht(w,(({ctrl:e,item:t,key:r})=>{uo(e,p),Xr(l,t,r)}))));const v=[];if(g&&ht(g,((e,t)=>{e.expired?(clearTimeout(e.expirationId),w.push(e)):~(t=v[t]=y.indexOf(e.key))&&(h[t]=e)})),ht(m,((e,t)=>{h[t]||(h[t]={key:y[t],item:e,phase:"mount",ctrl:new Go},h[t].ctrl.item=e)})),v.length){let e=-1;const{leave:r}=o?o():t;ht(v,((t,o)=>{const n=g[o];~t?(e=h.indexOf(n),h[e]={...n,item:m[t]}):r&&h.splice(++e,0,n)}))}pt.fun(a)&&h.sort(((e,t)=>a(e.item,t.item)));let b=-s;const _=Or(),x=no(t),j=new Map,M=(0,H.useRef)(new Map),S=(0,H.useRef)(!1);ht(h,((e,r)=>{const n=e.key,a=e.phase,l=o?o():t;let p,m;const h=Xr(l.delay||0,n);if("mount"==a)p=l.enter,m="enter";else{const e=y.indexOf(n)<0;if("leave"!=a)if(e)p=l.leave,m="leave";else{if(!(p=l.update))return;m="update"}else{if(e)return;p=l.enter,m="enter"}}if(p=Xr(p,e.item,r),p=pt.obj(p)?io(p):{to:p},!p.config){const t=d||x.config;p.config=Xr(t,e.item,r,m)}b+=s;const w={...x,delay:h+b,ref:u,immediate:l.immediate,reset:!1,...p};if("enter"==m&&pt.und(w.from)){const n=o?o():t,a=pt.und(n.initial)||g?n.from:n.initial;w.from=Xr(a,e.item,r)}const{onResolve:v}=w;w.onResolve=e=>{Xr(v,e);const t=f.current,r=t.find((e=>e.key===n));if(r&&(!e.cancelled||"update"==r.phase)&&r.ctrl.idle){const e=t.every((e=>e.ctrl.idle));if("leave"==r.phase){const t=Xr(i,r.item);if(!1!==t){const o=!0===t?0:t;if(r.expired=!0,!e&&o>0)return void(o<=2147483647&&(r.expirationId=setTimeout(_,o)))}}e&&t.some((e=>e.expired))&&(M.current.delete(r),c&&(S.current=!0),_())}};const A=Wo(e.ctrl,w);"leave"===m&&c?M.current.set(e,{phase:m,springs:A,payload:w}):j.set(e,{phase:m,springs:A,payload:w})}));const A=(0,H.useContext)(Xo),C=function(e){const t=(0,H.useRef)();return(0,H.useEffect)((()=>{t.current=e})),t.current}(A),k=A!==C&&function(e){for(const t in e)return!0;return!1}(A);Pr((()=>{k&&ht(h,(e=>{e.ctrl.start({default:A})}))}),[A]),ht(j,((e,t)=>{if(M.current.size){const e=h.findIndex((e=>e.key===t.key));h.splice(e,1)}})),Pr((()=>{ht(M.current.size?M.current:j,(({phase:e,payload:t},r)=>{const{ctrl:o}=r;r.phase=e,p?.add(o),k&&"enter"==e&&o.start({default:A}),t&&(function(e,t){t&&e.ref!==t&&(e.ref?.delete(e),t.add(e),e.ref=t)}(o,t.ref),!o.ref&&!p||S.current?(o.start(t),S.current&&(S.current=!1)):o.update(t))}))}),n?void 0:r);const N=e=>H.createElement(H.Fragment,null,h.map(((t,r)=>{const{springs:o}=j.get(t)||t.ctrl,n=e({...o},t.item,t,r);return n&&n.type?H.createElement(n.type,{...n.props,key:pt.str(t.key)||pt.num(t.key)?t.key:t.ctrl.id,ref:n.ref}):n})));return p?[N,p]:N}(t,{keys:e=>e.id,from:{opacity:0,height:0},enter:e=>async t=>await t({opacity:1,height:c.get(e).offsetHeight}),leave:()=>async e=>{await e({opacity:0}),await e({height:0})},immediate:s});r=(0,E.A)("components-snackbar-list",r);const d=e=>()=>{n(e.id),a(e.id)};return(0,i.jsxs)("div",{className:r,children:[o,u(((e,t)=>(0,i.jsx)(xn.div,{style:e,children:(0,i.jsx)("div",{className:"components-snackbar-list__notice-container",ref:e=>e&&c.set(t,e),children:(0,i.jsx)(An,{...(0,m.omit)(t,["content"]),onRemove:d(t),children:t.content})})})))]})},kn="woocommerce_admin_transient_notices_queue";function Nn(r){const{removeNotice:o}=(0,u.useDispatch)("core/notices"),{createNotice:n,removeNotice:a}=(0,u.useDispatch)("core/notices2"),{updateOptions:s}=(0,u.useDispatch)(t.optionsStore),{currentUser:c={},notices:l=[],notices2:d=[],noticesQueue:p={}}=(0,u.useSelect)((e=>({currentUser:e(t.userStore).getCurrentUser(),notices:e("core/notices").getNotices(),notices2:e("core/notices2").getNotices(),noticesQueue:e(t.optionsStore).getOption(kn)})));(0,e.useEffect)((()=>{Object.values(p).filter((e=>e.user_id===c.id||!e.user_id)).forEach((e=>{const t=(0,fe.applyFilters)("woocommerce_admin_queued_notice_filter",e);n(t.status,t.content,{onDismiss:()=>{(e=>{const t={...p};delete t[e],s({[kn]:t})})(t.id)},...t.options||{}})}))}),[]);const{className:m}=r,h=(0,E.A)("woocommerce-transient-notices","components-notices__snackbar",m),f=l.concat(d);return(0,i.jsx)(w.WooFooterItem,{children:(0,i.jsx)(Cn,{notices:f,className:h,onRemove:o,onRemove2:a})})}var En=s(22480);const In=[{imageUrl:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNyIgaGVpZ2h0PSIyNiIgdmlld0JveD0iMCAwIDI3IDI2IiBmaWxsPSJub25lIj4KICA8cGF0aCBkPSJNMjEuNTY0NSAxNi4zNTIzVjkuNjQ3NjNDMjEuNTY0NSA4LjMxMTUyIDIwLjgwNjEgNy41NjUyMiAxOS40NyA3LjU2NTIySDcuNTI5MjlDNi4xOTMxOCA3LjU2NTIyIDUuNDM0ODQgOC4zMTE1MiA1LjQzNDg0IDkuNjQ3NjNWMTYuMzUyM0M1LjQzNDg0IDE3LjY4ODQgNi4xOTMxOCAxOC40MzQ3IDcuNTI5MjkgMTguNDM0N0gxOS40N0MyMC44MDYxIDE4LjQzNDcgMjEuNTY0NSAxNy42ODg0IDIxLjU2NDUgMTYuMzUyM1oiIGZpbGw9IiNEMUMxRkYiLz4KICA8cGF0aCBkPSJNMjEuNTY0NSA5Ljc0MzkxSDUuNDM0ODhWMTEuODg2NUgyMS41NjQ1VjkuNzQzOTFaIiBmaWxsPSIjODczRUZGIi8+CiAgPHBhdGggZD0iTTE1LjAwNDYgMjEuOTU1N0w5LjQ5MTY3IDE3LjkxMTJWMjAuMzQyN0g4Ljg4OTgxQzYuMDQ5MDcgMjAuMzQyNyAzLjczNzk2IDE4LjAzMTYgMy43Mzc5NiAxNS4xOTA5VjE0LjA4MzVWMTQuNTg5QzMuNzM3OTYgMTMuMjY0OSAzLjY4OTgxIDExLjkxNjggMS45OTI1OSAxMS45MTY4SDAuNVYxNS4xMzY3QzAuNSAxOS44MTMxIDQuMjY3NTkgMjMuNTgwNyA4Ljg4OTgxIDIzLjU4MDdIOS40OTE2N1YyNi4wMDAxTDE1LjAwNDYgMjEuOTU1N1oiIGZpbGw9IiM4NzNFRkYiLz4KICA8cGF0aCBkPSJNOC44ODk1MiAyMy41ODA2SDkuNDkxMzhWMjAuMzQyN0w0LjU4MjY3IDIyLjM4NDJDNS44NDI5NSAyMy4xNDM3IDcuMzE2MjggMjMuNTgwNiA4Ljg4OTUyIDIzLjU4MDZaIiBmaWxsPSIjM0MwODdFIi8+CiAgPHBhdGggZD0iTTExLjk5NTEgNC4wNDQ0NUwxNy41MDgxIDguMDg4OVY1LjY1NzQxSDE4LjExQzIwLjk1MDcgNS42NTc0MSAyMy4yNjE4IDcuOTY4NTMgMjMuMjYxOCAxMC44MDkzVjExLjkxNjdWMTEuNDExMUMyMy4yNjE4IDEyLjczNTIgMjMuMzEgMTQuMDgzMyAyNS4wMDcyIDE0LjA4MzNIMjYuNDk5OFYxMC44NjM0QzI2LjQ5OTggNi4xODcwNCAyMi43MzIyIDIuNDE5NDUgMTguMTEgMi40MTk0NUgxNy41MDgxVjYuNjc1NzJlLTA2TDExLjk5NTEgNC4wNDQ0NVoiIGZpbGw9IiM4NzNFRkYiLz4KICA8cGF0aCBkPSJNMTguMTEgMi40MTk0MUgxNy41MDgxVjUuNjU3MzhMMjIuNDE2OCAzLjYxNTg5QzIxLjE1NjUgMi44NTYzNiAxOS42ODMyIDIuNDE5NDEgMTguMTEgMi40MTk0MVoiIGZpbGw9IiMzQzA4N0UiLz4KPC9zdmc+",title:(0,En.bb)("banner"),textTitle:(0,v.__)("30-day money-back guarantee","woocommerce")},{imageUrl:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNiIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI2IDI0IiBmaWxsPSJub25lIj4KICA8cGF0aCBkPSJNMjUuOTk5OSAwLjM2MzY0SDguNjMxNjhWMTIuMjM3MkgxNS44Njk2TDIyLjc0NTEgMTcuNjYzNVYxMi4yMzcySDI1Ljk5OTlWMC4zNjM2NFoiIGZpbGw9IiM4NzNFRkYiLz4KICA8cGF0aCBkPSJNMTUuMTYxOSA2LjY0NjM4SDIuMTAwNDZDMC43NTk1MzcgNi42NDYzOCAwIDcuMzg5NzQgMCA4LjcxODAzVjE3LjYyNjVDMCAxOC45NTM2IDAuNzU5NTM3IDE5LjY5ODEgMi4xMDA0NiAxOS42OTgxSDMuMjIxMTFWMjMuOTk5OUw4LjY3MTQ4IDE5LjY5ODFIMTUuMTYwNkMxNi41MDI4IDE5LjY5ODEgMTcuMjYxMSAxOC45NTQ4IDE3LjI2MTEgMTcuNjI2NVY4LjcxODAzQzE3LjI2MTEgNy4zOTA5MyAxNi41MDE2IDYuNjQ2MzggMTUuMTYwNiA2LjY0NjM4SDE1LjE2MTlaIiBmaWxsPSIjRDFDMUZGIi8+CiAgPHBhdGggZD0iTTQuMDQ0NDggMTQuMjQxQzQuNjQyNzIgMTQuMjQxIDUuMTI3ODIgMTMuNzYwOSA1LjEyNzgyIDEzLjE2ODlDNS4xMjc4MiAxMi41NzY4IDQuNjQyNzIgMTIuMDk2NyA0LjA0NDQ4IDEyLjA5NjdDMy40NDYyNCAxMi4wOTY3IDIuOTYxMTUgMTIuNTc2OCAyLjk2MTE1IDEzLjE2ODlDMi45NjExNSAxMy43NjA5IDMuNDQ2MjQgMTQuMjQxIDQuMDQ0NDggMTQuMjQxWiIgZmlsbD0iIzg3M0VGRiIvPgogIDxwYXRoIGQ9Ik04LjYzMjk1IDE0LjI0MUM5LjIzMTIgMTQuMjQxIDkuNzE2MjkgMTMuNzYwOSA5LjcxNjI5IDEzLjE2ODlDOS43MTYyOSAxMi41NzY4IDkuMjMxMiAxMi4wOTY3IDguNjMyOTUgMTIuMDk2N0M4LjAzNDcxIDEyLjA5NjcgNy41NDk2MiAxMi41NzY4IDcuNTQ5NjIgMTMuMTY4OUM3LjU0OTYyIDEzLjc2MDkgOC4wMzQ3MSAxNC4yNDEgOC42MzI5NSAxNC4yNDFaIiBmaWxsPSIjODczRUZGIi8+CiAgPHBhdGggZD0iTTEzLjIyMDMgMTQuMjQxQzEzLjgxODUgMTQuMjQxIDE0LjMwMzYgMTMuNzYwOSAxNC4zMDM2IDEzLjE2ODlDMTQuMzAzNiAxMi41NzY4IDEzLjgxODUgMTIuMDk2NyAxMy4yMjAzIDEyLjA5NjdDMTIuNjIyMSAxMi4wOTY3IDEyLjEzNyAxMi41NzY4IDEyLjEzNyAxMy4xNjg5QzEyLjEzNyAxMy43NjA5IDEyLjYyMjEgMTQuMjQxIDEzLjIyMDMgMTQuMjQxWiIgZmlsbD0iIzg3M0VGRiIvPgo8L3N2Zz4=",title:(0,En.KH)("banner"),textTitle:(0,v.__)("Get help when you need it","woocommerce")},{imageUrl:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMyIgaGVpZ2h0PSIyNiIgdmlld0JveD0iMCAwIDIzIDI2IiBmaWxsPSJub25lIj4KICA8cGF0aCBkPSJNMTEuMjcyOCAwQzguMzMwNjUgMy4yNTk3OSA0LjU5Njg2IDQuNDgwNTYgMC4zNDA5NDIgNC40ODA1NlYxMi44ODM3QzAuMzQwOTQyIDIwLjM2MDEgNS44NDYzMSAyNC42ODE2IDExLjI3MjggMjZDMTYuNjk5MiAyNC42ODI4IDIyLjIwNDYgMjAuMzYxMyAyMi4yMDQ2IDEyLjg4MzdWNC40ODA1NkMxNy45NDg3IDQuNDgwNTYgMTQuMjE0OSAzLjI1OTc5IDExLjI3MjggMFoiIGZpbGw9IiM4NzNFRkYiLz4KICA8cGF0aCBkPSJNMTAuODQ0IDE3Ljc0NzRMNy43MjQ2MSAxMy4wNjA4TDguNjM2NSAxMi40NjMxTDEwLjgxOTcgMTUuNzQzNEwxNS40MDcxIDguNDc5MDJMMTYuMzMzNiA5LjA1NTA2TDEwLjg0NCAxNy43NDc0WiIgZmlsbD0iI0QxQzFGRiIvPgo8L3N2Zz4=",title:(0,En.TF)("banner"),textTitle:(0,v.__)("Products you can trust","woocommerce")},{imageUrl:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNyIgaGVpZ2h0PSIyNyIgdmlld0JveD0iMCAwIDI3IDI3IiBmaWxsPSJub25lIj4KICA8cGF0aCBkPSJNMTMuMjg3MiAyLjIzODFDNi42OTgwOSAyLjIzODEgMS4zNTYwNSA3LjU4MDE0IDEuMzU2MDUgMTQuMTY5MkMxLjM1NjA1IDIwLjc1ODMgNi42OTgwOSAyNi4xMDAzIDEzLjI4NzIgMjYuMTAwM0MxOS44NzYyIDI2LjEwMDMgMjUuMjE4MyAyMC43NTgzIDI1LjIxODMgMTQuMTY5MkMyNS4yMTgzIDcuNTgwMTQgMTkuODc3NCAyLjIzODEgMTMuMjg3MiAyLjIzODFaIiBmaWxsPSIjRDFDMUZGIi8+CiAgPHBhdGggZD0iTTE1Ljc4NzMgOC4zNDY5M0MxNC4xNzc5IDYuOTY2MjkgMTEuNzU0OSA3LjE1MDQ1IDEwLjM3NDIgOC43NTk4QzguOTkzNTcgMTAuMzY5MiA5LjE3NzczIDEyLjc5MjIgMTAuNzg3MSAxNC4xNzI5QzEyLjM5NjQgMTUuNTUzNSAxNC44MTk1IDE1LjM2OTMgMTYuMjAwMSAxMy43NkMxNy41ODA4IDEyLjE1MDYgMTcuMzk2NiA5LjcyNzU4IDE1Ljc4NzMgOC4zNDY5M1oiIGZpbGw9IiM4NzNFRkYiLz4KICA8cGF0aCBkPSJNMTMuMjg3MiAxNi4xOTg3QzE2LjAxODQgMTYuMTk4NyAxOS43NTM1IDE3LjAzNDEgMTkuNzUzNSAyMC41Mjg1QzE5Ljc1MzUgMjEuMDMxNiAxOS43NTM1IDIyLjY4MTkgMTkuNzUzNSAyNC4xOTczQzE3Ljg5MDIgMjUuNDAxIDE1LjY3MDUgMjYuMTAwNCAxMy4yODcyIDI2LjEwMDRDMTAuOTAzOSAyNi4xMDA0IDguNjg0MjMgMjUuNDAxIDYuODIwODkgMjQuMTk3M0M2LjgyMDg5IDIyLjcyNCA2LjgyMDg5IDIxLjEwNzQgNi44MjA4OSAyMC41Mjg1QzYuODIwODkgMTcuMDM1MyAxMC41NTYgMTYuMTk4NyAxMy4yODcyIDE2LjE5ODdaIiBmaWxsPSIjODczRUZGIi8+Cjwvc3ZnPg==",title:(0,v.__)("Support the ecosystem","woocommerce"),textTitle:(0,v.__)("Support the ecosystem","woocommerce")}];function Tn(){const[t,r]=(0,e.useState)(0),[o,n]=(0,e.useState)(!1),[a,s]=(0,e.useState)(!0),c=(0,e.useRef)(null);(0,e.useEffect)((()=>{let e;return a&&(e=setInterval((()=>{r((e=>(e+1)%In.length))}),5e3)),()=>clearInterval(e)}),[a]),(0,e.useEffect)((()=>{const e=localStorage.getItem("wc_featuredBannerDismissed");n("true"===e)}),[]);const l=()=>s(!1),u=()=>s(!0),d=e=>{"ArrowRight"===e.key?(r((e=>(e+1)%In.length)),setTimeout((()=>{c.current?.focus()}),100)):"ArrowLeft"===e.key&&(r((e=>(e-1+In.length)%In.length)),setTimeout((()=>{c.current?.focus()}),100))};return o?null:(0,i.jsxs)("div",{className:"woocommerce-marketplace__banner",role:"region","aria-roledescription":"carousel","aria-label":(0,v.__)("Marketplace features with four slides","woocommerce"),onMouseEnter:l,onMouseLeave:u,onFocus:l,onBlur:u,children:[(0,i.jsx)("div",{className:"carousel-container",children:(0,i.jsx)("ul",{className:"carousel-list",children:In.map(((e,r)=>(0,i.jsxs)("li",{ref:r===t?c:null,id:`carousel-slide-${r}`,className:"carousel-slide "+(r===t?"active":""),"aria-roledescription":"slide","aria-hidden":r!==t,"aria-live":"off","aria-posinset":r+1,"aria-setsize":In.length,"aria-label":`${e.textTitle} - ${(0,v.__)("Slide","woocommerce")} ${r+1} ${(0,v.__)("of","woocommerce")} ${In.length}`,tabIndex:r===t?0:-1,onKeyDown:d,children:[(0,i.jsx)("img",{src:e.imageUrl,alt:"",className:"woocommerce-marketplace__banner-image"}),(0,i.jsx)("h3",{className:"woocommerce-marketplace__banner-title",children:e.title})]},r)))})}),(0,i.jsx)(f.Button,{className:"dismiss-button",onClick:()=>{localStorage.setItem("wc_featuredBannerDismissed","true"),n(!0),(0,g.recordEvent)("marketplace_features_banner_dismissed",{active_slide:In[t].textTitle})},"aria-label":(0,v.__)("Dismiss Marketplace features carousel","woocommerce"),children:(0,i.jsx)(f.Icon,{icon:"no-alt"})})]})}const Pn=({page:r})=>{const{activePlugins:n,installedPlugins:a,isJetpackConnected:s}=(0,u.useSelect)((e=>{const r=e(t.pluginsStore);return{activePlugins:r.getActivePlugins(),isJetpackConnected:r.isJetpackConnected(),installedPlugins:r.getInstalledPlugins()}}),[]),c=(0,d.zy)(),l=(0,d.RQ)(c.pathname),p={params:(0,d.g)(),url:l?.pathname},v=c.pathname.includes("/extensions");!function(t){(0,e.useEffect)((()=>{if(!t.path)return;const e=`woocommerce-admin-page_${"/"===(r=t.path)?"_home":r.replace(/:[a-zA-Z?]+/g,(function(e){return(t=e,t.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))).replace(":","");var t})).replace(/\//g,"_")}`;var r;return document.body.classList.add(e),()=>{document.body.classList.remove(e)}}),[t.path])}(r),(0,e.useEffect)((()=>{(0,o.triggerExitPageCesSurvey)()}),[]),(0,e.useEffect)((()=>{!function(){const{pathname:e}=c;if(!e)return;let t=e.substring(1).replace(/\//g,"_");0===t.length&&(t="home_screen"),(0,g.recordPageView)(t,{jetpack_installed:a.includes("jetpack"),jetpack_active:n.includes("jetpack"),jetpack_connected:s})}(),setTimeout((()=>{(0,o.triggerExitPageCesSurvey)()}),0)}),[c?.pathname]);const{breadcrumbs:b,layout:_={header:!0,footer:!0,showPluginArea:!0}}=r,{header:x=!0,footer:j=!0,showPluginArea:M=!0}=_,S=(0,h.getQuery)();(0,e.useEffect)((()=>{const e=document.getElementById("wpbody");x?e?.classList.remove("no-header"):e?.classList.add("no-header")}),[x]);const A=S.page&&"wc-admin"===S.page&&!S.path&&!S.task;return(0,e.useEffect)((()=>{const e="yes"===window.sessionStorage.getItem("lysWaiting");A&&e&&(0,h.navigateTo)({url:(0,h.getNewPath)({},"/launch-your-store")})}),[A]),(0,i.jsx)(w.LayoutContextProvider,{value:(0,w.getLayoutContextValue)([r?.navArgs?.id?.toLowerCase()||"page"]),children:(0,i.jsxs)(f.SlotFillProvider,{children:[(0,i.jsxs)("div",{className:"woocommerce-layout",children:[v&&(0,i.jsx)(Tn,{}),x&&(0,i.jsx)(Qe,{sections:(0,m.isFunction)(b)?b({match:p}):b,query:S}),(0,i.jsx)(Nn,{}),(0,i.jsx)(me,{showNotices:r?.layout?.showNotices,showStoreAlerts:r?.layout?.showStoreAlerts,children:(0,i.jsx)("div",{className:"woocommerce-layout__main",children:(0,i.jsx)(Pe,{page:r,match:p,query:S})})}),j&&(0,i.jsx)(He,{}),(0,i.jsx)(o.CustomerEffortScoreModalContainer,{})]}),M&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(y.PluginArea,{scope:"woocommerce-admin"}),(0,i.jsx)(y.PluginArea,{scope:"woocommerce-tasks"})]})]})})},On=(0,C.Qk)("dataEndpoints"),Dn=(0,l.compose)(window.wcSettings.admin?(0,t.withOptionsHydration)({...(0,C.Qk)("preloadOptions",{})}):m.identity,(0,t.withPluginsHydration)({...(0,C.Qk)("plugins",{}),jetpackStatus:On&&On.jetpackStatus||!1}))((()=>{const{currentUserCan:e}=(0,t.useUser)(),r=function(){const[e,t]=(0,H.useState)(Te);return(0,H.useEffect)((()=>{const e=`woocommerce/woocommerce/watch_${Ie}`;return(0,fe.addAction)("hookAdded",e,(e=>{e===Ie&&(0,fe.didFilter)(Ie)>0&&t(Te())})),()=>{(0,fe.removeAction)("hookAdded",e)}}),[]),e}(),o=document.location.pathname,n=o.substring(0,o.lastIndexOf("/"));return(0,i.jsx)(p.rI,{history:(0,h.getHistory)(),children:(0,i.jsx)(d.BV,{basename:n,children:r.filter((t=>!t.capability||e(t.capability))).map((e=>(0,i.jsx)(d.qh,{path:e.path||"",exact:!0,element:(0,i.jsx)(Pn,{page:e})},e.path)))})})}));function Ln(e,t){let r=parseInt(e.slice(1,3),16),o=parseInt(e.slice(3,5),16),n=parseInt(e.slice(5,7),16);return r=Math.floor(255*(1-t)+t*r),o=Math.floor(255*(1-t)+t*o),n=Math.floor(255*(1-t)+t*n),"#"+r.toString(16).padStart(2,"0")+o.toString(16).padStart(2,"0")+n.toString(16).padStart(2,"0")}var Rn=s(86817);class zn extends H.Component{constructor(e){super(e),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){this.setState({errorInfo:t}),(0,g.bumpStat)("error","unhandled-js-error-during-render");const r=t.componentStack?.trim().split("\n").slice(0,10).map((e=>e.trim()));(0,Rn.captureException)(e,{severity:"critical",extra:{componentStack:r}})}handleRefresh=()=>{window.location.reload()};handleOpenSupport=()=>{window.open("https://wordpress.org/support/plugin/woocommerce/","_blank")};render(){return this.state.hasError?(0,i.jsxs)("div",{className:"woocommerce-global-error-boundary",children:[(0,i.jsx)("h1",{className:"woocommerce-global-error-boundary__heading",children:(0,v.__)("Oops, something went wrong","woocommerce")}),(0,i.jsx)("p",{className:"woocommerce-global-error-boundary__subheading",children:(0,v.__)("We’re sorry for the inconvenience. Please try reloading the page, or you can get support from the community forums.","woocommerce")}),(0,i.jsxs)("div",{className:"woocommerce-global-error-boundary__actions",children:[(0,i.jsx)(f.Button,{variant:"secondary",onClick:this.handleOpenSupport,children:(0,v.__)("Get Support","woocommerce")}),(0,i.jsx)(f.Button,{variant:"primary",onClick:this.handleRefresh,children:(0,v.__)("Reload Page","woocommerce")})]}),(0,i.jsxs)("details",{className:"woocommerce-global-error-boundary__details",children:[(0,i.jsx)("summary",{children:(0,v.__)("Click for error details","woocommerce")}),(0,i.jsxs)("div",{className:"woocommerce-global-error-boundary__details-content",children:[(0,i.jsx)("strong",{className:"woocommerce-global-error-boundary__error",children:this.state.error&&this.state.error.toString()}),(0,i.jsx)("p",{children:this.state.errorInfo&&this.state.errorInfo.componentStack})]})]})]}):this.props.children}}(0,r.y)();const Fn=document.getElementById("root");if((()=>{const e=window.getComputedStyle(document.body).getPropertyValue("--wp-admin-theme-color").trim();document.documentElement.style.setProperty("--wp-admin-theme-color-background-04",Ln(e,.04)),document.documentElement.style.setProperty("--wp-admin-theme-color-background-25",Ln(e,.25))})(),Fn){const r="wc_admin";let n=(0,t.withSettingsHydration)(r,window.wcSettings.admin)(Dn);const a=window.wcSettings.admin?.preloadSettings?.general||!1;a&&(n=(0,t.withSettingsHydration)("general",{general:a})(n));const s=(0,C.Qk)("currentUserData");s&&(n=(0,t.withCurrentUserHydration)(s)(n)),(0,e.createRoot)(Fn).render((0,i.jsx)(zn,{children:(0,i.jsx)(n,{})})),Un=Fn,window.wcAdminFeatures&&!0===window.wcAdminFeatures["customer-effort-score-tracks"]&&(Un?(0,e.createRoot)(Un.insertBefore(document.createElement("div"),null)).render((0,i.jsx)(o.CustomerEffortScoreTracksContainer,{})):c("Customer Effort Score Tracks root not found"))}var Un})(),(window.wc=window.wc||{}).app={}})();