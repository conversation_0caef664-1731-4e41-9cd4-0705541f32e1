"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[12],{4439:(e,c,s)=>{s.r(c),s.d(c,{default:()=>b});var o=s(749),r=s(910),t=s(5460),n=s(7723),a=s(4530),l=s(559),i=s(2174),m=s(6087),d=s(4921),u=s(8889),h=s(4575),p=s(2919),k=s(4656),x=s(790);const j=({children:e,stepHeadingContent:c})=>(0,x.jsxs)("div",{className:"wc-block-components-checkout-step__heading",children:[(0,x.jsx)(k.Title,{"aria-hidden":"true",className:"wc-block-components-checkout-step__title",headingLevel:"2",children:e}),!!c&&(0,x.jsx)("span",{className:"wc-block-components-checkout-step__heading-content",children:c})]}),b=({children:e,className:c=""})=>{const{cartTotals:s}=(0,t.V)(),{isMedium:k,isSmall:b,isMobile:w}=(0,h.G)(),[_,y]=(0,m.useState)(!1),v=(0,r.getCurrencyFromPriceResponse)(s),N=parseInt(s.total_price,10),g=(0,m.useId)(),A=k||b||w?{role:"button",onClick:()=>y(!_),"aria-expanded":_,"aria-controls":g,tabIndex:0,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||y(!_)}}:{};return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsxs)("div",{className:c,children:[(0,x.jsxs)("div",{className:(0,d.A)("wc-block-components-checkout-order-summary__title",{"is-open":_}),...A,children:[(0,x.jsx)("p",{className:"wc-block-components-checkout-order-summary__title-text",role:"heading",children:(0,n.__)("Order summary","woocommerce")}),(0,x.jsx)(p.FormattedMonetaryAmount,{currency:v,value:N,className:"wc-block-components-checkout-order-summary__title-price"}),(0,x.jsx)("span",{className:"wc-block-components-checkout-order-summary__title-icon",children:(0,x.jsx)(a.A,{icon:_?l.A:i.A})})]}),(0,x.jsxs)("div",{className:(0,d.A)("wc-block-components-checkout-order-summary__content",{"is-open":_}),id:g,children:[e,(0,x.jsx)("div",{className:"wc-block-components-totals-wrapper",children:(0,x.jsx)(o.Ay,{currency:v,values:s})}),(0,x.jsx)(u.Xm,{})]})]}),(k||b||w)&&(0,x.jsx)(u.iG,{children:(0,x.jsxs)("div",{className:`${c} checkout-order-summary-block-fill-wrapper`,children:[(0,x.jsx)(j,{children:(0,x.jsx)(x.Fragment,{children:(0,n.__)("Order summary","woocommerce")})}),(0,x.jsxs)("div",{className:"checkout-order-summary-block-fill",children:[e,(0,x.jsx)("div",{className:"wc-block-components-totals-wrapper",children:(0,x.jsx)(o.Ay,{currency:v,values:s})}),(0,x.jsx)(u.Xm,{})]})]})})]})}},8889:(e,c,s)=>{s.d(c,{VM:()=>l,Xm:()=>n,iG:()=>a});var o=s(1e3),r=s(5460),t=s(790);const n=()=>{const{extensions:e,receiveCart:c,...s}=(0,r.V)(),n={extensions:e,cart:s,context:"woocommerce/checkout"};return(0,t.jsx)(o.ExperimentalOrderMeta.Slot,{...n})},{Fill:a,Slot:l}=(0,o.createSlotFill)("checkoutOrderSummaryActionArea")}}]);