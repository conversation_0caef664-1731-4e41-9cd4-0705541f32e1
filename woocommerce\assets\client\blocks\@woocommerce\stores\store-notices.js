import*as t from"@wordpress/interactivity";var e={d:(t,o)=>{for(var n in o)e.o(o,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:o[n]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};const o=(i={getContext:()=>t.getContext,getElement:()=>t.getElement,store:()=>t.store},a={},e.d(a,i),a),n=o.getContext,r="M12 3.2c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8 0-4.8-4-8.8-8.8-8.8zm0 16c-4 0-7.2-3.3-7.2-7.2C4.8 8 8 4.8 12 4.8s7.2 3.3 7.2 7.2c0 4-3.2 7.2-7.2 7.2zM11 17h2v-6h-2v6zm0-8h2V7h-2v2z",c={error:r,success:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z",notice:r},{state:s}=(0,o.store)("woocommerce/store-notices",{state:{get role(){const t=n();return"error"===t.notice.type||"success"===t.notice.type?"alert":"status"},get iconPath(){const t=n().notice.type;return c[t]},get isError(){const{notice:t}=n();return"error"===t.type},get isSuccess(){const{notice:t}=n();return"success"===t.type},get isInfo(){const{notice:t}=n();return"notice"===t.type},get notices(){const t=(0,o.getContext)("woocommerce/product-collection");if(t)return t?.notices;const e=n();return e&&e.notices?e.notices:[]}},actions:{addNotice:t=>{const{notices:e}=s,o=`${Date.now()}-${Math.random().toString(36).substring(2,15)}`,n={...t,id:o};return e.push(n),o},removeNotice:t=>{const{notices:e}=s;t="string"==typeof t?t:n().notice.id;const o=e.findIndex((({id:e})=>e===t));-1!==o&&e.splice(o,1)}},callbacks:{renderNoticeContent:()=>{const t=n(),{ref:e}=(0,o.getElement)();e&&(e.innerHTML=t.notice.notice)},scrollIntoView:()=>{const{ref:t}=(0,o.getElement)();t&&t.scrollIntoView({behavior:"smooth"})}}},{lock:!0});var i,a;