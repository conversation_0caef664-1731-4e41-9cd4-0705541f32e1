"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[2227],{78276:(e,s,t)=>{t.r(s),t.d(s,{default:()=>k});var l=t(4921),c=t(14656),i=t(78331),r=t(47143),o=t(47594),d=t(41616),n=t(94199),a=t(87792),h=t(32881),u=t(22953),p=t(89783),b=t(10790);const k=(0,d.withFilteredAttributes)(p.A)((({title:e,description:s,children:t,className:d})=>{const{showFormStepNumbers:p}=(0,n.O)(),{defaultFields:k}=(0,a.C)(),w=(0,h.b)(i.pt,k,"order"),S=(0,r.useSelect)((e=>e(o.checkoutStore).isProcessing()),[]);return 0===w.length||w.every((e=>!!e.hidden))?null:(0,b.jsxs)(c.FormStep,{id:"order-fields",disabled:S,className:(0,l.A)("wc-block-checkout__order-fields",d),title:e,description:s,showStepNumber:p,children:[(0,b.jsx)(u.A,{}),t]})}))}}]);