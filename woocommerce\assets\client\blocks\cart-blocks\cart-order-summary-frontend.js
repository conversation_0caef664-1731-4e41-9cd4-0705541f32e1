"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[9837],{7269:(e,s,c)=>{c.r(s),c.d(s,{default:()=>i});var t=c(749),r=c(910),a=c(5460),n=c(1e3),o=c(790);const l=()=>{const{extensions:e,receiveCart:s,...c}=(0,a.V)(),t={extensions:e,cart:c,context:"woocommerce/cart"};return(0,o.jsx)(n.ExperimentalOrderMeta.Slot,{...t})},i=({children:e,className:s=""})=>{const{cartTotals:c}=(0,a.V)(),n=(0,r.getCurrencyFromPriceResponse)(c);return(0,o.jsxs)("div",{className:s,children:[e,(0,o.jsx)("div",{className:"wc-block-components-totals-wrapper",children:(0,o.jsx)(t.Ay,{currency:n,values:c,isEstimate:!0})}),(0,o.jsx)(l,{})]})}}}]);