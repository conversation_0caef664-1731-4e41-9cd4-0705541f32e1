"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[146],{3185:(e,o,s)=>{s.r(o),s.d(o,{default:()=>i});var n=s(749),r=s(4656),t=s(910),c=s(5460),a=s(5954),u=s(1e3),l=s(790);const p=()=>{const{extensions:e,receiveCart:o,...s}=(0,c.V)(),n={extensions:e,cart:s,context:"woocommerce/cart"};return(0,l.jsx)(u.ExperimentalDiscountsMeta.Slot,{...n})},i=({className:e})=>{const{cartTotals:o,cartCoupons:s}=(0,c.V)(),{removeCoupon:u,isRemovingCoupon:i}=(0,a.k)("wc/cart");if(!s.length)return(0,l.jsx)(p,{});const C=(0,t.getCurrencyFromPriceResponse)(o);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(r.TotalsWrapper,{className:e,children:(0,l.jsx)(n.n$,{cartCoupons:s,currency:C,isRemovingCoupon:i,removeCoupon:u,values:o})}),(0,l.jsx)(p,{})]})}}}]);