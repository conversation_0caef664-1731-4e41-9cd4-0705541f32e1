/**
 * WooCustom Featured Video Frontend JavaScript
 * 
 * @package WooCustom
 * @since 1.0.0
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Initialize video functionality
    initFeaturedVideo();
    
    function initFeaturedVideo() {
        var $videoContainer = $('.woo-custom-featured-video-gallery');
        
        if ($videoContainer.length === 0) {
            return;
        }
        
        // Handle gallery navigation
        handleGalleryNavigation();
        
        // Handle video loading
        handleVideoLoading();
        
        // Handle responsive behavior
        handleResponsiveBehavior();
    }
    
    function handleGalleryNavigation() {
        // Pause video when gallery navigation occurs
        $(document).on('click', '.flex-control-thumbs li', function() {
            pauseAllVideos();
        });
        
        // Pause video when gallery arrows are clicked
        $(document).on('click', '.flex-direction-nav a', function() {
            pauseAllVideos();
        });
    }
    
    function handleVideoLoading() {
        var $videos = $('.woo-custom-featured-video iframe');
        
        $videos.each(function() {
            var $iframe = $(this);
            var $container = $iframe.closest('.woo-custom-featured-video');
            
            // Add loading class
            $container.addClass('loading');
            
            // Handle iframe load
            $iframe.on('load', function() {
                $container.removeClass('loading error');
            });
            
            // Handle iframe error
            $iframe.on('error', function() {
                $container.removeClass('loading').addClass('error');
            });
            
            // Timeout for loading
            setTimeout(function() {
                if ($container.hasClass('loading')) {
                    $container.removeClass('loading').addClass('error');
                }
            }, 10000); // 10 second timeout
        });
    }
    
    function handleResponsiveBehavior() {
        // Adjust video size on window resize
        $(window).on('resize', function() {
            adjustVideoSizes();
        });
        
        // Initial adjustment
        adjustVideoSizes();
    }
    
    function adjustVideoSizes() {
        var $videos = $('.woo-custom-featured-video');
        
        $videos.each(function() {
            var $video = $(this);
            var $container = $video.find('.video-container');
            
            // Ensure proper aspect ratio is maintained
            if ($video.hasClass('aspect-16-9')) {
                var width = $container.width();
                var height = width * (9/16);
                $container.css('padding-bottom', '56.25%');
            } else if ($video.hasClass('aspect-4-3')) {
                var width = $container.width();
                var height = width * (3/4);
                $container.css('padding-bottom', '75%');
            }
        });
    }
    
    function pauseAllVideos() {
        var $iframes = $('.woo-custom-featured-video iframe');
        
        $iframes.each(function() {
            var iframe = this;
            var src = iframe.src;
            
            // Pause YouTube videos
            if (src.includes('youtube.com')) {
                try {
                    iframe.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}', '*');
                } catch (e) {
                    // Fallback: reload iframe to stop video
                    iframe.src = iframe.src;
                }
            }
            
            // Pause Vimeo videos
            if (src.includes('vimeo.com')) {
                try {
                    iframe.contentWindow.postMessage('{"method":"pause"}', '*');
                } catch (e) {
                    // Fallback: reload iframe to stop video
                    iframe.src = iframe.src;
                }
            }
        });
    }
    
    // Handle WooCommerce gallery events
    $(document).on('woocommerce_gallery_init_zoom', function() {
        // Reinitialize video functionality after gallery zoom
        setTimeout(function() {
            initFeaturedVideo();
        }, 100);
    });
    
    // Handle variation changes
    $(document).on('found_variation', function() {
        // Pause videos when variation changes
        pauseAllVideos();
    });
    
    // Handle gallery image clicks
    $(document).on('click', '.woocommerce-product-gallery__image', function() {
        var $clicked = $(this);
        
        // If clicked element contains video, don't trigger lightbox
        if ($clicked.find('.woo-custom-featured-video').length > 0) {
            return false;
        }
    });
    
    // Prevent video from opening in lightbox
    $(document).on('click', '.woo-custom-featured-video-gallery a', function(e) {
        e.preventDefault();
        return false;
    });
    
    // Handle keyboard navigation
    $(document).on('keydown', function(e) {
        // Pause videos on ESC key
        if (e.keyCode === 27) {
            pauseAllVideos();
        }
    });
    
    // Handle page visibility change
    $(document).on('visibilitychange', function() {
        if (document.hidden) {
            pauseAllVideos();
        }
    });
    
    // Debug function
    function debugLog(message) {
        if (typeof console !== 'undefined' && console.log) {
            console.log('WooCustom Featured Video: ' + message);
        }
    }
    
    // Initialize on document ready
    debugLog('Featured Video initialized');
});
