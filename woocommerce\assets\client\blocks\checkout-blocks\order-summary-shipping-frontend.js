"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[8127],{594:(e,c,o)=>{o.r(c),o.d(c,{default:()=>h});var s=o(7723),l=o(749),a=o(5460),r=o(1e3),n=o(7143),t=o(7594),p=o(3932),i=o(4982),u=o(790);const h=({className:e=""})=>{const{cartNeedsShipping:c,shippingRates:o,shippingAddress:h}=(0,a.V)(),d=(0,n.useSelect)((e=>e(t.checkoutStore).prefersCollection()));if(!c)return null;const k=(0,p.PU)((0,p.uo)(o,null!=d&&d)),m=(0,i.Z$)(h);return(0,u.jsx)(r.TotalsWrapper,{className:e,children:(0,u.jsx)(l.w7,{label:k?(0,s.__)("Pickup","woocommerce"):(0,s.__)("Delivery","woocommerce"),placeholder:(0,u.jsx)("span",{className:"wc-block-components-shipping-placeholder__value",children:m?(0,s.__)("No available delivery option","woocommerce"):(0,s.__)("Enter address to calculate","woocommerce")})})})}}}]);