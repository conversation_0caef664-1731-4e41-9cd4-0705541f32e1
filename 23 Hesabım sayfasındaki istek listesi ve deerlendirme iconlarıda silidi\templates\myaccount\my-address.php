<?php
/**
 * My Addresses - Custom Card Design
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/myaccount/my-address.php.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.3.0
 */

defined( 'ABSPATH' ) || exit;

$customer_id = get_current_user_id();

if ( ! wc_ship_to_billing_address_only() && wc_shipping_enabled() ) {
	$get_addresses = apply_filters(
		'woocommerce_my_account_get_addresses',
		array(
			'billing'  => __( 'Fatura adresi', 'woo-custom' ),
			'shipping' => __( 'Gönderim adresi', 'woo-custom' ),
		),
		$customer_id
	);
} else {
	$get_addresses = apply_filters(
		'woocommerce_my_account_get_addresses',
		array(
			'billing' => __( 'Fatura adresi', 'woo-custom' ),
		),
		$customer_id
	);
}
?>

<div class="woo-custom-addresses-header">
	<h2 class="addresses-title"><?php esc_html_e( 'Adreslerim', 'woo-custom' ); ?></h2>
	<p class="addresses-description">
		<?php echo apply_filters( 'woocommerce_my_account_my_address_description', esc_html__( 'Aşağıdaki adresler ödeme sayfasında varsayılan olarak kullanılacaktır.', 'woo-custom' ) ); ?>
	</p>
</div>

<div class="woo-custom-addresses-container">
	<?php foreach ( $get_addresses as $name => $address_title ) : ?>
		<?php
			$address = wc_get_account_formatted_address( $name );
			$icon_class = ( 'billing' === $name ) ? 'fas fa-file-invoice' : 'fas fa-shipping-fast';
		?>

		<div class="woo-custom-address-card <?php echo esc_attr( $name ); ?>-address">
			<div class="address-card-header">
				<div class="address-icon">
					<i class="<?php echo esc_attr( $icon_class ); ?>"></i>
				</div>
				<div class="address-title-section">
					<h3 class="address-title"><?php echo esc_html( $address_title ); ?></h3>
					<span class="address-type"><?php echo ( 'billing' === $name ) ? esc_html__( 'Faturalama için', 'woo-custom' ) : esc_html__( 'Teslimat için', 'woo-custom' ); ?></span>
				</div>
				<div class="address-actions">
					<a href="<?php echo esc_url( wc_get_endpoint_url( 'edit-address', $name ) ); ?>" class="edit-address-btn">
						<i class="fas fa-edit"></i>
						<span><?php echo $address ? esc_html__( 'Düzenle', 'woo-custom' ) : esc_html__( 'Ekle', 'woo-custom' ); ?></span>
					</a>
				</div>
			</div>

			<div class="address-card-content">
				<?php if ( $address ) : ?>
					<div class="address-details">
						<?php echo wp_kses_post( $address ); ?>
					</div>
				<?php else : ?>
					<div class="address-empty">
						<div class="empty-icon">
							<i class="fas fa-map-marker-alt"></i>
						</div>
						<p class="empty-text"><?php esc_html_e( 'Henüz bu tür bir adres eklenmemiş.', 'woo-custom' ); ?></p>
						<a href="<?php echo esc_url( wc_get_endpoint_url( 'edit-address', $name ) ); ?>" class="add-address-btn">
							<i class="fas fa-plus"></i>
							<?php printf( esc_html__( '%s Ekle', 'woo-custom' ), esc_html( $address_title ) ); ?>
						</a>
					</div>
				<?php endif; ?>

				<?php
				/**
				 * Used to output content after core address fields.
				 *
				 * @param string $name Address type.
				 * @since 8.7.0
				 */
				do_action( 'woocommerce_my_account_after_my_address', $name );
				?>
			</div>

			<?php if ( $address ) : ?>
				<div class="address-card-footer">
					<div class="address-status">
						<i class="fas fa-check-circle"></i>
						<span><?php esc_html_e( 'Aktif adres', 'woo-custom' ); ?></span>
					</div>
				</div>
			<?php endif; ?>
		</div>

	<?php endforeach; ?>
</div>


