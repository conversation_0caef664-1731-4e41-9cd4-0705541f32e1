"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[53],{12974:(e,t,o)=>{o.d(t,{Ay:()=>a});var s=o(13240);const n=["a","b","em","i","strong","p","br"],r=["target","href","rel","name","download"],a=e=>({__html:(0,s.sanitize)(e,{ALLOWED_TAGS:n,ALLOWED_ATTR:r})})},1275:(e,t,o)=>{o.d(t,{v:()=>l});var s=o(18537),n=o(56427),r=o(86087),a=o(12974),i=o(1069),c=o(39793);const l=({method:e,paymentMethodsState:t,setPaymentMethodsState:o,isExpanded:l,initialVisibilityStatus:d,...m})=>{var u,p,_,h;const g=(0,r.useRef)(null);void 0===d&&null===g.current&&void 0!==t[e.id]&&(g.current=(0,i.TO)(e,t[e.id]));const y=void 0!==d?null!=d&&d:null!==(u=g.current)&&void 0!==u&&u;return l||y?(0,c.jsx)("div",{id:e.id,className:"woocommerce-list__item woocommerce-list__item-enter-done",...m,children:(0,c.jsxs)("div",{className:"woocommerce-list__item-inner",children:["apple_google"!==e.id&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{className:"woocommerce-list__item-before",children:(0,c.jsx)("img",{src:e.icon,alt:e.title+" logo"})}),(0,c.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,c.jsx)("span",{className:"woocommerce-list__item-title",children:e.title}),(0,c.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,a.Ay)((0,s.decodeEntities)(e.description))})]})]}),"apple_google"===e.id&&(0,c.jsxs)("div",{className:"woocommerce-list__item-multi",children:[(0,c.jsxs)("div",{className:"woocommerce-list__item-multi-row multi-row-space",children:[(0,c.jsx)("div",{className:"woocommerce-list__item-before",children:(0,c.jsx)("img",{src:e.icon,alt:e.title+" logo"})}),(0,c.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,c.jsx)("span",{className:"woocommerce-list__item-title",children:e.title}),(0,c.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,a.Ay)((0,s.decodeEntities)(e.description))})]})]}),(0,c.jsxs)("div",{className:"woocommerce-list__item-multi-row",children:[(0,c.jsx)("div",{className:"woocommerce-list__item-before",children:(0,c.jsx)("img",{src:e.extraIcon,alt:e.extraTitle+" logo"})}),(0,c.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,c.jsx)("span",{className:"woocommerce-list__item-title",children:e.extraTitle}),(0,c.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,a.Ay)((0,s.decodeEntities)(null!==(p=e.extraDescription)&&void 0!==p?p:""))})]})]})]}),(0,c.jsx)("div",{className:"woocommerce-list__item-after",children:(0,c.jsx)("div",{className:"woocommerce-list__item-after__actions wc-settings-prevent-change-event",children:(0,c.jsx)(n.ToggleControl,{checked:null!==(_=t[e.id])&&void 0!==_&&_,onChange:s=>{o({...t,[e.id]:s})},disabled:null!==(h=e.required)&&void 0!==h&&h,label:""})})})]})}):null}},7175:(e,t,o)=>{o.d(t,{A:()=>i});var s=o(56427),n=o(47804),r=o(56109),a=o(39793);function i({onClose:e}){return(0,a.jsxs)("div",{className:"settings-payments-onboarding-modal__header",children:[(0,a.jsx)("img",{src:`${r.GZ}images/woo-logo.svg`,alt:"Woo Logo",className:"settings-payments-onboarding-modal__header--logo"}),(0,a.jsx)(s.Button,{className:"settings-payments-onboarding-modal__header--close",onClick:e,children:(0,a.jsx)(s.Icon,{icon:n.A})})]})}},58016:(e,t,o)=>{o.d(t,{A:()=>h});var s=o(27723),n=o(33068),r=o(51609),a=o(87007),i=o(86087),c=o(4921),l=o(56109),d=o(39793);function m({label:e,isCompleted:t,isActive:o}){return(0,d.jsxs)("div",{className:(0,c.A)("settings-payments-onboarding-modal__sidebar--list-item",{"is-active":o,"is-completed":t}),children:[(0,d.jsx)("span",{className:"settings-payments-onboarding-modal__sidebar--list-item-icon",children:t?(0,d.jsx)("img",{src:l.GZ+"images/onboarding/icons/complete.svg",alt:(0,s.__)("Step completed","woocommerce")}):(0,d.jsx)("img",{src:l.GZ+"images/onboarding/icons/pending.svg",alt:(0,s.__)("Step active","woocommerce")})}),(0,d.jsx)("span",{className:"settings-payments-onboarding-modal__sidebar--list-item-label",children:e})]})}var u=o(1069);function p({active:e,steps:t,justCompletedStepId:o,includeSidebar:n=!1,sidebarTitle:r,context:a={}}){const c=t.find((t=>t.id===e));if((0,i.useEffect)((()=>{c&&(0,u.W7)("woopayments_onboarding_modal_step_view",{step:e,source:a?.sessionEntryPoint||"unknown"})}),[e]),!c)return null;const l=t.findIndex((t=>t.id===e))+1,p=e=>e.id===o||"completed"===e.status||l===t.length,_=t.sort(((e,t)=>{const o=p(e);return o===p(t)?0:o?-1:1}));return(0,d.jsxs)(d.Fragment,{children:[n&&(0,d.jsxs)("div",{className:"settings-payments-onboarding-modal__sidebar",children:[(0,d.jsxs)("div",{className:"settings-payments-onboarding-modal__sidebar--header",children:[(0,d.jsx)("h2",{className:"settings-payments-onboarding-modal__sidebar--header-title",children:r}),(0,d.jsx)("div",{className:"settings-payments-onboarding-modal__sidebar--header-steps",children:(0,s.sprintf)((0,s.__)("Step %1$s of %2$s","woocommerce"),l,t.length)})]}),(0,d.jsx)("div",{className:"settings-payments-onboarding-modal__sidebar--list",children:_.map((t=>(0,d.jsx)(m,{label:t.label,isCompleted:p(t),isActive:t.id===e},t.id)))})]}),(0,d.jsx)("div",{className:"settings-payments-onboarding-modal__content",children:(0,d.jsx)("div",{className:"settings-payments-onboarding-modal__step",id:c.id,children:c.content})})]})}var _=o(99096);function h({includeSidebar:e=!0}){const{steps:t,isLoading:o,currentStep:i,navigateToStep:c,justCompletedStepId:l,sessionEntryPoint:m}=(0,_.w)(),u=(0,n.zy)();return(0,r.useEffect)((()=>{var e;i&&!u.pathname.endsWith(null!==(e=i?.path)&&void 0!==e?e:"")&&c(i.id)}),[i,c,u.pathname]),o?(0,d.jsx)("div",{className:"settings-payments-onboarding-modal__loading",children:(0,d.jsx)(a.A,{})}):t&&t.length>0?(0,d.jsx)(n.BV,{children:(0,d.jsx)(n.qh,{path:"*",element:(0,d.jsx)("div",{className:"settings-payments-onboarding-modal__wrapper",children:(0,d.jsx)(p,{steps:t,active:null!==(h=i?.id)&&void 0!==h?h:"",justCompletedStepId:l,includeSidebar:e,sidebarTitle:(0,s.__)("Set up WooPayments","woocommerce"),context:{sessionEntryPoint:m}})})})}):null;var h}},87007:(e,t,o)=>{o.d(t,{A:()=>n}),o(51609);var s=o(39793);const n=()=>(0,s.jsx)("svg",{className:"stripe-spinner",width:"29",height:"29",viewBox:"0 0 29 29",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M14.3308 28.3333C14.0453 28.3333 13.7714 28.2199 13.5695 28.018C13.3675 27.816 13.2541 27.5422 13.2541 27.2566C13.2541 26.971 13.3675 26.6972 13.5695 26.4952C13.7714 26.2933 14.0453 26.1799 14.3308 26.1799C17.4727 26.1799 20.4859 24.9317 22.7076 22.7101C24.9293 20.4884 26.1774 17.4752 26.1774 14.3333C26.1774 11.1914 24.9293 8.17821 22.7076 5.95655C20.4859 3.73489 17.4727 2.48677 14.3308 2.48677C12.5754 2.48495 10.8416 2.87419 9.25541 3.62623C7.66923 4.37826 6.27049 5.4742 5.16082 6.83441C5.07124 6.94388 4.96097 7.03464 4.83631 7.1015C4.71165 7.16836 4.57504 7.21001 4.43428 7.22407C4.15001 7.25248 3.8661 7.16679 3.645 6.98587C3.42391 6.80494 3.28374 6.54359 3.25534 6.25932C3.22694 5.97505 3.31262 5.69114 3.49355 5.47005C4.80533 3.86303 6.45849 2.56827 8.33301 1.67977C10.2075 0.791267 12.2564 0.331321 14.3308 0.333319C22.0626 0.333319 28.3308 6.6015 28.3308 14.3333C28.3308 22.0651 22.0626 28.3333 14.3308 28.3333Z",fill:"#4F575D"})})},99096:(e,t,o)=>{o.d(t,{X:()=>m,w:()=>d});var s=o(86087),n=o(47143),r=o(40314),a=o(96476),i=o(39793);const c={buildStepURL:e=>(0,a.getNewPath)({path:e},e,{page:"wc-settings",tab:"checkout"}),preserveParams:["source","from"]},l=(0,s.createContext)({steps:[],isLoading:!0,currentStep:void 0,context:{},navigateToStep:()=>{},navigateToNextStep:()=>{},getStepByKey:()=>{},refreshStoreData:()=>{},closeModal:()=>{},justCompletedStepId:null,setJustCompletedStepId:()=>{},sessionEntryPoint:"",snackbar:{show:!1,duration:4e3,message:""},setSnackbar:()=>{}}),d=()=>(0,s.useContext)(l),m=({children:e,onboardingSteps:t,closeModal:o,onFinish:d,urlStrategy:m,sessionEntryPoint:u="settings_payments"})=>{const p=(0,a.getHistory)(),[_,h]=(0,s.useState)([]),[g,y]=(0,s.useState)(!0),[w,x]=(0,s.useState)([]),[b,v]=(0,s.useState)(null),[f,j]=(0,s.useState)({show:!1,duration:4e3,message:""}),S=(0,s.useCallback)((e=>{v(e)}),[]),{invalidateResolutionForStoreSelector:k}=(0,n.useDispatch)(r.woopaymentsOnboardingStore),{invalidateResolutionForStoreSelector:C}=(0,n.useDispatch)(r.paymentSettingsStore),{storeData:N,isStoreLoading:F}=(0,n.useSelect)((e=>({storeData:e(r.woopaymentsOnboardingStore).getOnboardingData(u),isStoreLoading:e(r.woopaymentsOnboardingStore).isOnboardingDataRequestPending()})),[u]),T=(0,s.useCallback)((e=>w.find((t=>t.id===e))),[w]),P=(0,s.useCallback)(((e,t)=>!e.dependencies||0===e.dependencies.length||e.dependencies.every((e=>{const o=t.find((t=>t.id===e));return"completed"===o?.status}))),[]),E=(0,s.useCallback)((e=>{const t=T(e);if(t?.path){const e=m||c,o=e.preserveParams?(0,a.getQuery)():{},s=e.preserveParams?.reduce(((e,t)=>(o[t]&&(e[t]=o[t]),e)),{})||{},n=e.buildStepURL(t.path,s);p.push(n)}}),[T,p,m]),A=w.find((e=>"completed"!==e.status&&P(e,w))),B=(0,s.useCallback)((()=>{const e=w.findIndex((e=>e.id===A?.id));if(-1!==e){if("completed"!==A?.status&&x(w.map((e=>e.id===A?.id?{...e,status:"completed"}:e))),e===w.length-1)return void d?.();const t=w.find((e=>"completed"!==e.status&&P(e,w)));t&&E(t.id)}}),[A,w,E,P]),W=()=>{h([]),y(!0),S(null),x([]),j({show:!1,message:""}),k("getOnboardingData")};return(0,s.useEffect)((()=>{!F&&N.steps.length>0&&(h(N.steps),y(!1))}),[N,F]),(0,s.useEffect)((()=>{const e=t.filter((e=>"backend"!==e.type||-1!==_.findIndex((t=>t.id===e.id)))).map((e=>{if("backend"===e.type){const t=_.find((t=>t.id===e.id));return Object.assign({},e,{status:t?.status||"not_started",dependencies:t?.dependencies||[],path:t?.path,context:t?.context,actions:t?.actions,errors:t?.errors})}return Object.assign({},e)})),o=e.map((t=>"frontend"===t.type?{...t,status:P(t,e)?"completed":"not_started"}:t));x(o)}),[_,P]),(0,s.useEffect)((()=>{W()}),[]),(0,i.jsx)(l.Provider,{value:{steps:w,context:N.context,isLoading:g,currentStep:A,navigateToStep:E,navigateToNextStep:B,getStepByKey:T,refreshStoreData:W,closeModal:()=>{o(),C("getPaymentProviders")},justCompletedStepId:b,setJustCompletedStepId:S,sessionEntryPoint:u,snackbar:f,setSnackbar:j},children:e})}},98404:(e,t,o)=>{o.d(t,{A:()=>re});var s=o(51609),n=o.n(s),r=o(99096),a=o(7175),i=o(66087),c=o(39793);const l=(e={})=>{const[t,o]=(0,s.useState)(e),[n,r]=(0,s.useState)({}),[a,c]=(0,s.useState)({});return{data:t,setData:e=>o((t=>({...t,...e}))),errors:n,setErrors:e=>r((t=>(0,i.omitBy)({...t,...e},i.isNil))),touched:a,setTouched:e=>c((t=>({...t,...e})))}},d=(0,s.createContext)(null),m=({children:e,initialData:t})=>(0,c.jsx)(d.Provider,{value:l(t),children:e}),u=()=>{const e=(0,s.useContext)(d);if(!e)throw new Error("useBusinessVerificationContext() must be used within <BusinessVerificationContextProvider>");return e};var p=o(56427),_=o(86087);const h=(0,s.createContext)(null),g=({children:e,onStepView:t,...o})=>{const a=(e=>e.reduce(((e,t,o)=>{var s;return n().isValidElement(t)&&(e[null!==(s=t.props.name)&&void 0!==s?s:o]=t),e}),{}))(e),i=(({steps:e,initialStep:t,onStepChange:o,onComplete:n,onExit:a})=>{const i=Object.keys(e),{currentStep:c}=(0,r.w)(),[l,d]=(0,s.useState)(null!=t?t:i[0]);if("completed"===c?.context?.sub_steps[l]?.status){const e=i.indexOf(l),t=i[e+1];d(t),o?.(t)}const m=(i.indexOf(l)+1)/i.length;return{currentStep:l,progress:m,nextStep:()=>{const e=i.indexOf(l),t=i[e+1];t?(d(t),o?.(t)):n?.()},prevStep:()=>{const e=i.indexOf(l),t=i[e-1];t?(d(t),o?.(t)):a?.()},exit:()=>a?.()}})({steps:a,...o});(0,_.useEffect)((()=>{t?.(i.currentStep)}),[i.currentStep]);const l=a[i.currentStep];return(0,c.jsx)(h.Provider,{value:i,children:l})},y=()=>{const e=(0,s.useContext)(h);if(!e)throw new Error("useStepperContext() must be used within <Stepper>");return e};var w=o(4921),x=o(24148),b=o(90700),v=o(72744),f=o(27723),j=o(21913);const S=e=>e?.name||"",k=({selectedItem:e},{type:t,changes:o,props:{items:s}})=>{switch(t){case j.WM.stateChangeTypes.ToggleButtonKeyDownArrowDown:return{selectedItem:s[e?Math.min(s.indexOf(e)+1,s.length-1):0]};case j.WM.stateChangeTypes.ToggleButtonKeyDownArrowUp:return{selectedItem:s[e?Math.max(s.indexOf(e)-1,0):s.length-1]};default:return o}},C=function({name:e,className:t,label:o,describedBy:s,options:n,onChange:r,value:a,placeholder:i,children:l}){const{getLabelProps:d,getToggleButtonProps:m,getMenuProps:u,getItemProps:h,isOpen:g,highlightedIndex:y,selectedItem:C}=(0,j.WM)({initialSelectedItem:n[0],items:n,itemToString:S,onSelectedItemChange:r,selectedItem:a||{},stateReducer:k}),N=S(C),F=u({className:"components-custom-select-control__menu","aria-hidden":!g}),T=(0,_.useCallback)((e=>{e.stopPropagation(),F?.onKeyDown?.(e)}),[F]);return F["aria-activedescendant"]?.startsWith("downshift-null")&&delete F["aria-activedescendant"],(0,c.jsxs)("div",{className:(0,w.A)("woopayments components-custom-select-control",t),children:[(0,c.jsx)("label",{...d({className:"components-custom-select-control__label"}),children:o}),(0,c.jsxs)(p.Button,{...m({"aria-label":o,"aria-labelledby":void 0,"aria-describedby":s||(N?(0,f.sprintf)((0,f.__)("Currently selected: %s","woocommerce"),N):(0,f.__)("No selection","woocommerce")),className:(0,w.A)("components-custom-select-control__button",{placeholder:!N}),name:e}),children:[(0,c.jsx)("span",{className:"components-custom-select-control__button-value",children:N||i}),(0,c.jsx)(x.A,{icon:b.A,className:"components-custom-select-control__button-icon"})]}),(0,c.jsx)("div",{...F,children:(0,c.jsx)("ul",{className:"components-custom-select-control__menu-container",onKeyDown:T,children:g&&n.map(((e,t)=>(0,c.jsxs)("li",{...h({item:e,index:t,className:(0,w.A)(e.className,"components-custom-select-control__item",{"is-highlighted":t===y}),style:e.style}),children:[l?l(e):e.name,e===C&&(0,c.jsx)(x.A,{icon:v.A,className:"components-custom-select-control__item-icon"})]},e.key)))})})]})};var N=o(56537);const F=({name:e,className:t,label:o,options:n,onChange:r,value:a,placeholder:i,searchable:l})=>{const d=(0,s.useRef)(null),m=(0,s.useRef)(),u=n.filter((e=>e.items?.length)).map((e=>e.key)),[p,_]=(0,s.useState)(new Set([u[0]])),[h,g]=(0,s.useState)(new Set([...u,...n[0]?.items||[]])),[y,S]=(0,s.useState)(""),k=n.filter((e=>h.has(e.key))),{isOpen:C,selectedItem:F,getToggleButtonProps:T,getMenuProps:P,getLabelProps:E,highlightedIndex:A,getItemProps:B}=(0,j.WM)({items:k,itemToString:e=>e?.name||"",selectedItem:a||{},onSelectedItemChange:r,stateReducer:(e,{changes:t,type:o})=>{if(l&&o===j.WM.stateChangeTypes.ToggleButtonKeyDownArrowDown)return e;if(t.selectedItem&&t.selectedItem.items){if(y)return e;const o=t.selectedItem.key;return p.has(o)?(p.delete(o),t.selectedItem.items.forEach((e=>h.delete(e)))):(p.add(o),t.selectedItem.items.forEach((e=>h.add(e)))),_(p),g(h),e}return t}}),W=P({className:"components-grouped-select-control__list","aria-hidden":!C,onFocus:()=>d.current?.focus(),onBlur:e=>{e.relatedTarget===d.current&&(e.nativeEvent.preventDownshiftDefault=!0)},onKeyDown:e=>{"Space"===e.code&&(e.nativeEvent.preventDownshiftDefault=!0)}});return(0,c.jsxs)("div",{className:(0,w.A)("woopayments components-grouped-select-control",t),children:[(0,c.jsx)("label",{...E({className:"components-grouped-select-control__label"}),children:o}),(0,c.jsxs)("button",{...T({type:"button",className:(0,w.A)("components-text-control__input components-grouped-select-control__button",{placeholder:!F?.name}),name:e}),children:[(0,c.jsx)("span",{className:"components-grouped-select-control__button-value",children:F?.name||i}),(0,c.jsx)(x.A,{icon:b.A,className:"components-grouped-select-control__button-icon"})]}),(0,c.jsx)("div",{...W,children:C&&(0,c.jsxs)(c.Fragment,{children:[l&&(0,c.jsx)("input",{className:"components-grouped-select-control__search",ref:d,type:"text",value:y,onChange:({target:e})=>{if(m.current||(m.current={visibleItems:h}),""===e.value)g(m.current.visibleItems),m.current=void 0;else{const t=n.filter((t=>t?.group&&`${t.name} ${t.context||""}`.toLowerCase().includes(e.value.toLowerCase()))),o=t.map((e=>e?.group||"")),s=new Set([...t.map((e=>e.key)),...o]);g(s)}S(e.value)},tabIndex:-1,placeholder:(0,f.__)("Search…","woocommerce")}),(0,c.jsx)("ul",{className:"components-grouped-select-control__list-container",children:k.map(((e,t)=>{const o=!!e.items;return(0,c.jsxs)("li",{...B({item:e,index:t,className:(0,w.A)("components-grouped-select-control__item",e.className,{"is-highlighted":t===A},{"is-group":o})}),children:[(0,c.jsx)("div",{className:"components-grouped-select-control__item-content",children:e.name}),e.key===F?.key&&(0,c.jsx)(x.A,{icon:v.A}),!y&&o&&(0,c.jsx)(x.A,{icon:p.has(e.key)?N.A:b.A})]},e.key)}))})]})})]})},T=(e,t,o)=>{const{error:s,...n}=t;return s?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(e,{...n,ref:o,className:(0,w.A)(n.className,"has-error")}),(0,c.jsx)("div",{className:"components-form-field__error",children:s})]}):(0,c.jsx)(e,{...n,ref:o})},P=((0,s.forwardRef)(((e,t)=>T(p.TextControl,e,t))),e=>T(C,e)),E=e=>T(F,e),A={generic:{individual:(0,f.__)("Select if you run your own business as an individual and are self-employed","woocommerce"),company:(0,f.__)("Select if you filed documentation to register your business with a government agency","woocommerce"),non_profit:(0,f.__)("Select if you run a non-business entity","woocommerce"),government_entity:(0,f.__)("Select if your business is classed as a government entity","woocommerce")},US:{individual:(0,f.__)("Select if you run your own business as an individual and are self-employed","woocommerce"),company:(0,f.__)("Select if you filed documentation to register your business with a government agency","woocommerce"),non_profit:(0,f.__)("Select if you have been granted tax-exempt status by the Internal Revenue Service (IRS)","woocommerce"),government_entity:(0,f.__)("Select if your business is classed as a government entity","woocommerce")}},B=e=>{const t=window.wcSettings.admin?.onboarding?.profile?.industry?.[0];if(t)return e[t]},W=()=>{const{woocommerce_share_key:e,woocommerce_coming_soon:t,woocommerce_private_link:o}=window.wcSettings?.admin?.siteVisibilitySettings||{};return"yes"!==t||"no"===o?"":e?"?woo-share="+e:""};var I=o(1455),D=o.n(I);var L=o(36849);const O={steps:{activate:{heading:(0,f.__)("Start accepting real payments","woocommerce"),subheading:(0,L.A)({mixedString:(0,f.__)("You are currently testing payments on your store. To activate real payments, you will need to provide some additional details about your business. {{link}}Learn more{{/link}}.","woocommerce"),components:{link:(0,c.jsx)("a",{rel:"external noopener noreferrer",target:"_blank",href:"https://woocommerce.com/document/woopayments/startup-guide/#sign-up-process"})}}),cta:(0,f.__)("Activate payments","woocommerce")},business:{heading:(0,f.__)("Let’s get your store ready to accept payments","woocommerce"),subheading:(0,f.__)("We’ll use these details to enable payments for your store. This information can’t be changed after your account is created.","woocommerce")},store:{heading:(0,f.__)("Please share a few more details","woocommerce"),subheading:(0,f.__)("This info will help us speed up the set up process.","woocommerce")},loading:{heading:(0,f.__)("One last step! Verify your identity with our partner","woocommerce"),subheading:(0,f.__)("This will take place in a secure environment through our partner. Once your business details are verified, you’ll be redirected back to your store dashboard.","woocommerce"),cta:(0,f.__)("Finish your verification process","woocommerce")},embedded:{heading:(0,f.__)("One last step! Verify your identity with our partner","woocommerce"),subheading:(0,f.__)("This info will verify your account","woocommerce")}},fields:{country:(0,f.__)("Where is your business located?","woocommerce"),business_type:(0,f.__)("What type of legal entity is your business?","woocommerce"),"company.structure":(0,f.__)("What category of legal entity identify your business?","woocommerce"),mcc:(0,f.__)("What type of goods or services does your business sell? ","woocommerce")},errors:{generic:(0,f.__)("Please provide a response","woocommerce"),country:(0,f.__)("Please provide a country","woocommerce"),business_type:(0,f.__)("Please provide a business type","woocommerce"),mcc:(0,f.__)("Please provide a type of goods or services","woocommerce")},placeholders:{generic:(0,f.__)("Select an option","woocommerce"),country:(0,f.__)("Select a country","woocommerce")},tos:(0,L.A)({mixedString:(0,f.sprintf)((0,f.__)("By using %1$s, you agree to be bound by our {{tosLink}}Terms of Service{{/tosLink}} (including {{merchantTermsLink}}%2$s merchant terms{{/merchantTermsLink}}) and acknowledge that you have read our {{privacyPolicyLink}}Privacy Policy{{/privacyPolicyLink}}.","woocommerce"),"WooPayments","WooPay"),components:{tosLink:(0,c.jsx)("a",{rel:"external noopener noreferrer",target:"_blank",href:"https://wordpress.com/tos/"}),merchantTermsLink:(0,c.jsx)("a",{rel:"external noopener noreferrer",target:"_blank",href:"https://wordpress.com/tos/#more-woopay-specifically"}),privacyPolicyLink:(0,c.jsx)("a",{rel:"external noopener noreferrer",target:"_blank",href:"https://automattic.com/privacy/"})}}),continue:(0,f.__)("Continue","woocommerce"),back:(0,f.__)("Back","woocommerce"),cancel:(0,f.__)("Cancel","woocommerce")},M=e=>{const{data:t,errors:o,setErrors:n,touched:r,setTouched:a}=u(),i=(o=t[e])=>{r[e]||a({[e]:!0});const s=((e,t)=>!!t)(0,o)?void 0:O.errors[e]||O.errors.generic;n({[e]:s})};return(0,s.useEffect)((()=>(i(),t[e]||a({[e]:!1}),()=>n({[e]:void 0}))),[]),{validate:i,error:()=>r[e]?o[e]:void 0}};var R=o(1069);const z=({children:e})=>{const{data:t,errors:o,touched:n,setTouched:a}=u(),{currentStep:l,sessionEntryPoint:d}=(0,r.w)(),{nextStep:m}=y(),[_,h]=(0,s.useState)(!1);return(0,c.jsxs)("form",{onSubmit:async e=>{var s,r;e.preventDefault(),await((0,i.isEmpty)(o)&&(e=>["business_type","country","mcc"].every((t=>Boolean(e[t]))))(t)?(h(!0),((e,t,o)=>t?D()({url:t,method:"POST",data:{sub_steps:{...o,[e]:{status:"completed"}}}}):Promise.resolve())("business",null!==(s=l?.actions?.save?.href)&&void 0!==s?s:void 0,null!==(r=l?.context?.sub_steps)&&void 0!==r?r:{}).then((()=>((0,R.W7)("woopayments_onboarding_modal_kyc_sub_step_completed",{sub_step_id:"business",country:t.country||"unknown",business_type:t.business_type||"unknown",mcc:t.mcc||"unknown",source:d}),h(!1),m()))).catch((()=>{h(!1)}))):(a((0,i.mapValues)(n,(()=>!0))),Promise.resolve()))},children:[e,(0,c.jsx)(p.Button,{variant:"primary",type:"submit",className:"stepper__cta",onClick:()=>{var e;(0,R.W7)("woopayments_onboarding_modal_click",{step_id:null!==(e=l?.id)&&void 0!==e?e:"unknown",sub_step_id:"business",action:"business_form_continue",source:d})},isBusy:_,disabled:_,children:O.continue})]})},q=({onChange:e,...t})=>{var o;const{name:s}=t,{data:n,setData:r}=u(),{validate:a,error:i}=M(s);return(0,c.jsx)(P,{label:O.fields[s],value:t.options?.find((e=>e.key===n[s])),placeholder:null!==(o=O.placeholders[s])&&void 0!==o?o:O.placeholders.generic,onChange:({selectedItem:t})=>{e?e?.(s,t):r({[s]:t?.key}),a(t?.key)},options:[],error:i(),...t})},H=({onChange:e,...t})=>{var o;const{name:s}=t,{data:n,setData:r}=u(),{validate:a,error:i}=M(s);return(0,c.jsx)(E,{label:O.fields[s],value:t.options?.find((e=>e.key===n[s])),placeholder:null!==(o=O.placeholders[s])&&void 0!==o?o:O.placeholders.generic,onChange:({selectedItem:t})=>{e?e?.(s,t):r({[s]:t?.key}),a(t?.key)},options:[],error:i(),...t})},V=()=>{var e;const{data:t,setData:o}=u(),{currentStep:s}=(0,r.w)(),n=(e=>Object.entries(e||[]).map((([e,t])=>({key:e,name:t,types:[]}))).sort(((e,t)=>e.name.localeCompare(t.name))))(s?.context?.fields?.available_countries||{}),a=(e=>(e||[]).map((e=>({...e,types:e.types.map((t=>({...t,description:A[e.key]?A[e.key][t.key]:A.generic[t.key]})))}))).sort(((e,t)=>e.name.localeCompare(t.name)))||[])(s?.context?.fields?.business_types||[]),i=((null!==(e=s?.context?.fields?.mccs_display_tree)&&void 0!==e?e:[])||[]).filter((e=>!!e?.items&&(e.items?.filter((e=>!e?.items))||[]).length)).reduce(((e,t)=>{const o=t.items?.map((e=>({key:e.id,name:e.title,group:t.id,context:e?.keywords?e.keywords.join(" "):""})))||[];return[...e,{key:t.id,name:t.title,items:o.map((e=>e.key))},...o]}),[]),l=a.find((e=>"PR"===t.country?"US"===e.key:e.key===t.country)),d=l?.types.sort(((e,t)=>"company"===e.key?-1:"company"===t.key?1:0)),m=d?.find((e=>e.key===t.business_type)),p=0===m?.structures.length||m?.structures.find((e=>e.key===t["company.structure"])),_=e=>{o(e);const t=s?.actions?.save?.href;return t?D()({url:t,method:"POST",data:{self_assessment:e}}):Promise.resolve()},h=(e,t)=>{let o={[e]:t?.key};return"business_type"===e?o={...o,"company.structure":void 0}:"country"===e&&(o={...o,business_type:void 0}),_(o)};return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("span",{"data-testid":"country-select",children:(0,c.jsx)(q,{name:"country",options:n,onChange:h})}),l&&l.types.length>0&&(0,c.jsx)("span",{"data-testid":"business-type-select",children:(0,c.jsx)(q,{name:"business_type",options:l.types,onChange:h,children:e=>(0,c.jsxs)("div",{children:[(0,c.jsx)("div",{children:e.name}),(0,c.jsx)("div",{className:"complete-business-info-task__option-description",children:e.description})]})})}),m&&m.structures.length>0&&(0,c.jsx)("span",{"data-testid":"business-structure-select",children:(0,c.jsx)(q,{name:"company.structure",options:m.structures,onChange:h})}),l&&m&&p&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("span",{"data-testid":"mcc-select",children:(0,c.jsx)(H,{name:"mcc",options:i,onChange:(e,o)=>{const s={...t,[e]:o?.key};return _(s)},searchable:!0})}),(0,c.jsx)("span",{className:"woopayments-onboarding__tos",children:O.tos})]})]})};var U=o(87007),K=o(20195),G=o(73290),$=o(94736),J=o(92991),Y=o(8181);v.A,J.A,$.A,G.A;const Z=({children:e,actions:t=[],className:o,status:s="info",isDismissible:n=!0,onRemove:r})=>{((e,t)=>{const o="string"==typeof t?t:(0,_.renderToString)(t),s="error"===e?"assertive":"polite";(0,_.useEffect)((()=>{o&&(0,K.speak)(o,s)}),[o,s])})(s,e);const a=(0,w.A)(o,"woopayments-banner-notice","is-"+s);return(0,c.jsxs)("div",{className:a,children:[(0,c.jsxs)("div",{className:"woopayments-banner-notice__content",children:[e,t.length>0&&(0,c.jsx)("div",{className:"woopayments-banner-notice__actions",children:t.map((({className:e,label:t,variant:o,onClick:s,url:n,urlTarget:r},a)=>{let i=o;return"primary"!==o&&(i=n?"link":"secondary"),(0,c.jsx)(p.Button,{href:n,variant:i,onClick:n?void 0:s,className:e,target:r,children:t},a)}))})]}),n&&(0,c.jsx)(p.Button,{className:"woopayments-banner-notice__dismiss",icon:(0,c.jsx)(Y.A,{}),label:(0,f.__)("Dismiss this notice","woocommerce"),onClick:()=>r?.(),showTooltip:!1})]})};var X=o(2929),Q=o(86948);const ee={variables:{colorPrimary:"#873EFF",colorBackground:"#FFFFFF",buttonPrimaryColorBackground:"#3858E9",buttonPrimaryColorBorder:"#3858E9",buttonPrimaryColorText:"#FFFFFF",buttonSecondaryColorBackground:"#FFFFFF",buttonSecondaryColorBorder:"#3858E9",buttonSecondaryColorText:"#3858E9",colorText:"#101517",colorSecondaryText:"#50575E",actionPrimaryColorText:"#3858E9",actionSecondaryColorText:"#101517",colorBorder:"#DDDDDD",formHighlightColorBorder:"#3858E9",formAccentColor:"#3858E9",colorDanger:"#CC1818",offsetBackgroundColor:"#F0F0F0",formBackgroundColor:"#FFFFFF",badgeNeutralColorText:"#2C3338",badgeNeutralColorBackground:"#F6F7F7",badgeNeutralColorBorder:"#F6F7F7",badgeSuccessColorText:"#005C12",badgeSuccessColorBackground:"#EDFAEF",badgeSuccessColorBorder:"#EDFAEF",badgeWarningColorText:"#614200",badgeWarningColorBackground:"#FCF9E8",badgeWarningColorBorder:"#FCF9E8",badgeDangerColorText:"#8A2424",badgeDangerColorBackground:"#FCF0F1",badgeDangerColorBorder:"#FCF0F1",borderRadius:"2px",buttonBorderRadius:"2px",formBorderRadius:"2px",badgeBorderRadius:"2px",overlayBorderRadius:"8px",spacingUnit:"10px",fontFamily:"-apple-system, BlinkMacSystemFont, 'system-ui', 'Segoe UI', 'Helvetica Neue', 'Helvetica', 'Roboto', 'Arial', sans-serif",fontSizeBase:"16px",headingXlFontSize:"32px",headingXlFontWeight:"400",headingLgFontSize:"24px",headingLgFontWeight:"400",headingMdFontSize:"20px",headingMdFontWeight:"400",headingSmFontSize:"13px",headingSmFontWeight:"600",headingXsFontSize:"12px",headingXsFontWeight:"600",bodyMdFontWeight:"400",bodyMdFontSize:"16px",bodySmFontSize:"13px",bodySmFontWeight:"400",labelSmFontSize:"12px",labelSmFontWeight:"200",labelMdFontSize:"13px"}},te=({onboardingData:e,onExit:t,onLoaderStart:o,onLoadError:n,onStepChange:a,collectPayoutRequirements:l=!1})=>{const{stripeConnectInstance:d,initializationError:m}=(e=>{const[t,o]=(0,s.useState)(null),{currentStep:n}=(0,r.w)(),[a,c]=(0,s.useState)(null),[l,d]=(0,s.useState)(!0);return(0,s.useEffect)((()=>{(async()=>{try{var t;const s=await(async(e,t)=>{const o=(s=e,(0,i.toPairs)(s).reduce(((e,[t,o])=>null!==o?(0,i.set)(e,t,o):e),{}));var s;const n={};return Object.keys(o).length>0&&(n.self_assessment=o),await D()({url:t,method:"POST",data:n})})(e,null!==(t=n?.actions?.kyc_session?.href)&&void 0!==t?t:""),{clientSecret:r,publishableKey:a}=s.session;if(!a)throw new Error((0,f.__)("Unable to start onboarding. If this problem persists, please contact support.","woocommerce"));const c=(0,X.e)({publishableKey:a,fetchClientSecret:async()=>r,appearance:{overlays:"drawer",...ee},locale:s.session.locale.replace("_","-")});o(c)}catch(e){c(e instanceof Error?e.message:(0,f.__)("Unable to start onboarding. If this problem persists, please contact support.","woocommerce"))}finally{d(!1)}})()}),[e]),{stripeConnectInstance:t,initializationError:a,loading:l}})(e);return(0,c.jsxs)(c.Fragment,{children:[m&&(0,c.jsx)(Z,{status:"error",children:m}),d&&(0,c.jsx)(Q.MT,{connectInstance:d,children:(0,c.jsx)(Q.hw,{onLoaderStart:o,onLoadError:n,onExit:t,onStepChange:e=>a?.(e.step),collectionOptions:{fields:l?"eventually_due":"currently_due",futureRequirements:"omit"}})})]})},oe=({collectPayoutRequirements:e=!1})=>{var t;const{data:o}=u(),{currentStep:n,navigateToNextStep:a,sessionEntryPoint:i}=(0,r.w)(),[l,d]=(0,s.useState)(!1),[m,p]=(0,s.useState)(!0),[_,h]=(0,s.useState)(null),g=null!==(t=n?.actions?.kyc_fallback?.href)&&void 0!==t?t:"";return(0,c.jsxs)(c.Fragment,{children:[_&&("invalid_request_error"===_.error.type?(0,c.jsx)(Z,{className:"woopayments-banner-notice--embedded-kyc",status:"warning",isDismissible:!1,actions:[{label:"Learn more",variant:"primary",url:"https://woocommerce.com/document/woopayments/startup-guide/#requirements",urlTarget:"_blank"},{label:"Cancel",variant:"link",url:g}],children:(0,f.__)("Payment activation through our financial partner requires HTTPS and cannot be completed.","woocommerce")}):(0,c.jsx)(Z,{className:"woopayments-banner-notice--embedded-kyc",status:"error",isDismissible:!1,children:_.error.message})),m&&(0,c.jsx)("div",{className:"embedded-kyc-loader-wrapper padded",children:(0,c.jsx)(U.A,{})}),l&&(0,c.jsx)("div",{className:"embedded-kyc-loader-wrapper",children:(0,c.jsx)(U.A,{})}),(0,c.jsx)(te,{onExit:async()=>{d(!0);try{var e;(await(async e=>await D()({url:e,method:"POST",data:{}}))(null!==(e=n?.actions?.kyc_session_finish?.href)&&void 0!==e?e:"")).success?a():window.location.href=g}catch(e){window.location.href=g}},onStepChange:t=>{(0,R.W7)("woopayments_onboarding_modal_kyc_step_change",{kyc_step_id:t,collect_payout_requirements:e,source:i})},onLoaderStart:()=>{(0,R.W7)("woopayments_onboarding_modal_kyc_started_loading",{collect_payout_requirements:e,source:i}),p(!1)},onLoadError:t=>{(0,R.W7)("woopayments_onboarding_modal_kyc_load_error",{error_type:t.error.type,error_message:t.error.message||"no_message",collect_payout_requirements:e,source:i}),h(t)},onboardingData:o,collectPayoutRequirements:e})]})},se=()=>{const{currentStep:e,sessionEntryPoint:t}=(0,r.w)(),{nextStep:o}=y(),[n,a]=(0,s.useState)(!1);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("h1",{className:"stepper__heading",children:O.steps.activate.heading}),(0,c.jsx)("p",{className:"stepper__subheading",children:O.steps.activate.subheading}),(0,c.jsx)("div",{className:"stepper__content",children:(0,c.jsx)(p.Button,{variant:"primary",className:"stepper__cta",onClick:()=>{(0,R.W7)("woopayments_onboarding_modal_click",{step:e?.id||"unknown",sub_step_id:"activate",action:"activate_payments",source:t}),a(!0),(0,R.wJ)().then((()=>(a(!1),o()))).catch((()=>{a(!1)}))},isBusy:n,disabled:n,children:O.steps.activate.cta})})]})},ne=({name:e,children:t,showHeading:o=!0})=>(0,c.jsx)(c.Fragment,{children:(0,c.jsxs)("div",{className:"stepper__wrapper",children:[o&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("h1",{className:"stepper__heading",children:O.steps[e].heading}),(0,c.jsx)("h2",{className:"stepper__subheading",children:O.steps[e].subheading})]}),(0,c.jsx)("div",{className:"stepper__content",children:t})]})}),re=()=>{var e,t,o;const{currentStep:s,closeModal:n,sessionEntryPoint:i}=(0,r.w)(),l={business_name:window.wcSettings?.siteTitle,mcc:B(null!==(e=s?.context?.fields?.mccs_display_tree)&&void 0!==e?e:[]),site:"localhost"===location.hostname?"https://wcpay.test":window.wcSettings?.homeUrl+W(),country:s?.context?.fields?.location,...null!==(t=s?.context?.self_assessment)&&void 0!==t?t:{}},d=null!==(o=s?.context?.has_test_account)&&void 0!==o&&o,u=[...d?["activate"]:[],"business","embedded"].find((e=>"completed"!==s?.context?.sub_steps[e]?.status));return(0,c.jsxs)("div",{className:"settings-payments-onboarding-modal__step-business-verification",children:[(0,c.jsx)(a.A,{onClose:n}),(0,c.jsx)("div",{className:"settings-payments-onboarding-modal__step-business-verification-content",children:(0,c.jsx)(m,{initialData:l,children:(0,c.jsxs)(g,{initialStep:u,onStepView:e=>{(0,R.W7)("woopayments_onboarding_modal_step_view",{step:s?.id||"unknown",sub_step_id:e,source:i})},onStepChange:()=>{window.scroll(0,0)},onExit:()=>{(0,R.W7)("woopayments_onboarding_modal_step_exit",{step:s?.id||"unknown",source:i})},onComplete:()=>{(0,R.W7)("woopayments_onboarding_modal_step_complete",{step:s?.id||"unknown",source:i})},children:[d&&(0,c.jsx)(ne,{name:"activate",showHeading:!1,children:(0,c.jsx)(se,{})}),(0,c.jsx)(ne,{name:"business",children:(0,c.jsx)(z,{children:(0,c.jsx)(V,{})})}),(0,c.jsx)(ne,{name:"embedded",showHeading:!1,children:(0,c.jsx)(oe,{})})]})})})]})}},8148:(e,t,o)=>{o.d(t,{A:()=>l}),o(51609);var s=o(27723),n=o(56427),r=o(99096),a=o(7175),i=o(1069),c=o(39793);const l=()=>{const{context:e,closeModal:t,sessionEntryPoint:o}=(0,r.w)();return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(a.A,{onClose:t}),(0,c.jsx)("div",{className:"settings-payments-onboarding-modal__step--content",children:(0,c.jsxs)("div",{className:"settings-payments-onboarding-modal__step--content-finish",children:[(0,c.jsx)("h1",{className:"settings-payments-onboarding-modal__step--content-finish-title",children:(0,s.__)("You’re ready to accept payments!","woocommerce")}),(0,c.jsx)("p",{className:"settings-payments-onboarding-modal__step--content-finish-description",children:(0,s.__)("Great news — your WooPayments account has been activated. You can now start accepting payments on your store.","woocommerce")}),(0,c.jsx)(n.Button,{variant:"primary",className:"settings-payments-onboarding-modal__step--content-finish-primary-button",onClick:()=>{var t;(0,i.W7)("woopayments_onboarding_modal_click",{step:"finish",action:"go_to_payments_overview",source:o}),window.location.href=null!==(t=e?.urls?.overview_page)&&void 0!==t?t:""},children:(0,s.__)("Go to Payments Overview","woocommerce")}),(0,c.jsxs)("div",{className:"divider",children:[(0,c.jsx)("span",{className:"divider-line"}),(0,c.jsx)("span",{className:"divider-text",children:(0,s.__)("OR","woocommerce")}),(0,c.jsx)("span",{className:"divider-line"})]}),(0,c.jsx)(n.Button,{variant:"secondary",className:"settings-payments-onboarding-modal__step--content-finish-secondary-button",onClick:()=>{(0,i.W7)("woopayments_onboarding_modal_click",{step:"finish",action:"close_window",source:o}),t()},children:(0,s.__)("Close this window","woocommerce")})]})})]})}},91289:(e,t,o)=>{o.d(t,{A:()=>h});var s=o(27723),n=o(56427),r=o(83306),a=o(86087),i=o(1455),c=o.n(i),l=o(47804),d=o(4921),m=o(99096),u=o(1275),p=o(1069),_=o(39793);function h(){const{currentStep:e,navigateToNextStep:t,closeModal:o,sessionEntryPoint:i}=(0,m.w)(),[h,g]=(0,a.useState)(!1),[y,w]=(0,a.useState)({}),[x,b]=(0,a.useState)(null),[v,f]=(0,a.useState)(!1),j=e?.context?.pms_state,S=e?.context?.recommended_pms,k=(0,a.useMemo)((()=>S?(0,p.js)(S):[]),[S]),C=(0,a.useRef)(null),[N,F]=(0,a.useState)(!1);(0,a.useEffect)((()=>{j&&w(j)}),[j]);const T=(0,a.useMemo)((()=>(0,p.LI)(y)),[y]);(0,a.useEffect)((()=>{if(null===x&&k.length>0&&Object.keys(T).length>0&&k.every((e=>void 0!==T[e.id]))){const e={};k.forEach((t=>{e[t.id]=(0,p.TO)(t,T[t.id])})),b(e)}}),[k,T,x]);const P=(0,a.useMemo)((()=>!x||h?0:k.filter((e=>{var t;return!(null!==(t=x[e.id])&&void 0!==t&&t)})).length),[k,h,x]),E=t=>{const o=e?.actions?.save?.href;return o?c()({url:o,method:"POST",data:{payment_methods:t}}).then((()=>{w(t)})):(w(t),Promise.resolve())},A=()=>setTimeout((()=>{const e=C.current;if(e){const t=e.scrollHeight>e.clientHeight;F(t)}}),10);return(0,a.useEffect)((()=>{let e=A();const t=()=>{clearTimeout(e),e=A()};return window.addEventListener("resize",t),()=>{clearTimeout(e),window.removeEventListener("resize",t)}}),[h,x]),(0,_.jsx)("div",{className:"settings-payments-onboarding-modal__step--content",children:(0,_.jsxs)("div",{className:"woocommerce-recommended-payment-methods",children:[(0,_.jsxs)("div",{className:"woocommerce-recommended-payment-methods__header",children:[(0,_.jsxs)("div",{className:"woocommerce-recommended-payment-methods__header--title",children:[(0,_.jsx)("h1",{className:"components-truncate components-text",children:(0,s.__)("Choose your payment methods","woocommerce")}),(0,_.jsx)(n.Button,{className:"settings-payments-onboarding-modal__header--close",onClick:o,children:(0,_.jsx)(n.Icon,{icon:l.A})})]}),(0,_.jsx)("div",{className:"woocommerce-recommended-payment-methods__header--description",children:(0,s.__)("Select which payment methods you'd like to offer to your shoppers. You can update these at any time.","woocommerce")})]}),(0,_.jsx)("div",{className:"woocommerce-recommended-payment-methods__list",children:(0,_.jsxs)("div",{className:"settings-payments-methods__container",ref:C,children:[(0,_.jsx)("div",{className:"woocommerce-list",children:k?.map((e=>{var t;return(0,_.jsx)(u.v,{method:e,paymentMethodsState:(0,p.LI)(y),setPaymentMethodsState:e=>{E(e)},initialVisibilityStatus:x&&null!==(t=x[e.id])&&void 0!==t?t:null,isExpanded:h},e.id)}))}),!h&&P>0&&(0,_.jsx)("div",{className:"settings-payments-methods__show-more--wrapper",children:(0,_.jsx)(n.Button,{className:"settings-payments-methods__show-more",onClick:()=>{(0,p.W7)("woopayments_onboarding_modal_click",{step:e?.id||"unknown",action:"show_more",hidden_count:P,source:i}),g(!h)},tabIndex:0,"aria-expanded":h,children:(0,s.sprintf)((0,s.__)("Show more (%s)","woocommerce"),P)})})]})}),(0,_.jsx)("div",{className:(0,d.A)("woocommerce-recommended-payment-methods__list_footer",{"has-border":N}),children:(0,_.jsx)(n.Button,{className:"components-button is-primary",onClick:()=>{const o=e?.actions?.finish?.href;o&&(f(!0),E(y).then((()=>c()({url:o,method:"POST"}))).then((()=>{var e;const o={displayed_payment_methods:Object.keys(y).join(", "),selected_payment_methods:Object.keys(y).filter((e=>y[e])).join(", "),deselected_payment_methods:Object.keys(y).filter((e=>!y[e])).join(", "),business_country:null!==(e=window.wcSettings?.admin?.woocommerce_payments_nox_profile?.business_country_code)&&void 0!==e?e:"unknown",source:i};(0,p.W7)("woopayments_onboarding_modal_click",{step:"payment_methods",action:"continue",...o}),(0,r.recordEvent)("wcpay_settings_payment_methods_continue",o),f(!1),t()})).catch((()=>{f(!1)})))},isBusy:v,disabled:v,children:(0,s.__)("Continue","woocommerce")})})]})})}},33623:(e,t,o)=>{o.d(t,{A:()=>b});var s=o(51609),n=o(1455),r=o.n(n),a=o(85816),i=o(27723),c=o(36849),l=o(56427),d=o(98846),m=o(96476),u=o(7175),p=o(99096),_=o(56109),h=o(1069),g=o(68345),y=o(39793);const w=({progress:e,title:t,message:o})=>(0,y.jsx)(a.Loader,{className:"woocommerce-payments-test-account-step__preloader",children:(0,y.jsxs)(a.Loader.Layout,{className:"woocommerce-payments-test-account-step__preloader-layout",children:[(0,y.jsx)(a.Loader.Illustration,{children:(0,y.jsx)("img",{src:`${_.GZ}images/onboarding/test-account-setup.svg`,alt:"setup",style:{maxWidth:"223px"}})}),(0,y.jsx)(a.Loader.Title,{children:t||(0,i.__)("Finishing payments setup","woocommerce")}),(0,y.jsx)(a.Loader.ProgressBar,{progress:null!=e?e:0}),(0,y.jsx)(a.Loader.Sequence,{interval:0,children:o||(0,i.__)("In just a few moments, you'll be ready to test payments on your store.","woocommerce")})]})}),x=[(0,i.__)("Setting up your test account","woocommerce"),(0,i.__)("Finishing payments setup","woocommerce"),(0,i.__)("Almost there!","woocommerce")],b=()=>{const{currentStep:e,navigateToNextStep:t,closeModal:o,refreshStoreData:n,setJustCompletedStepId:a,sessionEntryPoint:b,setSnackbar:v}=(0,p.w)(),[f,j]=(0,s.useState)("idle"),[S,k]=(0,s.useState)(20),[C,N]=(0,s.useState)(),[F,T]=(0,s.useState)(0),[P,E]=(0,s.useState)(0),[A,B]=(0,s.useState)(x[0]),[W,I]=(0,s.useState)(!1),[D,L]=(0,s.useState)(),O=(0,s.useRef)(null),M=(0,s.useRef)(null),R=(0,s.useRef)(null),z=(0,s.useRef)(0);(0,s.useEffect)((()=>{if("success"===f&&(0,h.W7)("woopayments_onboarding_modal_step_view",{step:e?.id||"unknown",sub_step_id:"ready_to_test_payments",source:b}),"polling"!==f&&"initializing"!==f)return void(z.current=0);0===z.current&&B(x[0]);const t=setTimeout((()=>{z.current+=1,z.current<x.length&&B(x[z.current])}),5e3);return()=>{clearTimeout(t)}}),[f]);const q=()=>{null!==O.current&&(clearTimeout(O.current),O.current=null),null!==R.current&&(clearTimeout(R.current),R.current=null)},[H,V]=(0,s.useState)(!1),U=(0,s.useCallback)((()=>{j("idle"),k(0),N(void 0),T(0),M.current=null,q()}),[j,k,N,T]);if((0,s.useEffect)((()=>{if("idle"===f){if("completed"===e?.status)return j("success"),a(e.id),void k(100);if("blocked"===e?.status)return N(e?.errors?.[0]?.message||(0,i.__)("There are environment or store setup issues which are blocking progress. Please resolve them to proceed.","woocommerce")),void j("blocked");"not_started"===e?.status||"failed"===e?.status?(j("initializing"),k(10),(async()=>{e?.actions?.clean?.href&&(P>0||"failed"===e?.status)&&await r()({url:e?.actions?.clean?.href,method:"POST"})})().then((()=>r()({url:e?.actions?.init?.href,method:"POST"}))).then((e=>{e?.success?j("polling"):(L(e?.code||""),N(e?.message||(0,i.__)("Creating test account failed. Please try again.","woocommerce")),j("error"))})).catch((e=>{L(e?.code||""),N(e.message),j("error")}))):j("polling")}if("polling"===f){const t=()=>{q(),r()({url:e?.actions?.check?.href,method:"POST"}).then((o=>{if("completed"===o?.status)return void(O.current=window.setTimeout((()=>{j("success"),k(100),a(e?.id||"")}),1e3));let s,n,r=0;k((e=>(r=0===F?Math.min(e+5,90):1===F?Math.min(e+1,96):e,r))),0===F&&r>=90?(s=1,n=5e3,M.current=Date.now()):1===F?M.current&&Date.now()-M.current>3e4?(s=2,n=7e3):(s=1,n=5e3):2===F?(s=2,n=7e3):(s=0,n=3e3),T(s),O.current=window.setTimeout(t,n)})).catch((e=>{N(e.message),j("error"),q()}))};t()}return"initializing"===f&&null===R.current&&(R.current=window.setInterval((()=>{k((e=>e<30?Math.min(e+2,30):e))}),1e3)),"initializing"!==f&&null!==R.current&&(clearTimeout(R.current),R.current=null),()=>{q()}}),[f,e,P,F,a]),"success"===f)return(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(u.A,{onClose:o}),(0,y.jsx)("div",{className:"settings-payments-onboarding-modal__step--content",children:(0,y.jsx)("div",{className:"woocommerce-payments-test-account-step__success_content_container",children:(0,y.jsxs)("div",{className:"woocommerce-woopayments-modal__content woocommerce-payments-test-account-step__success_content",children:[(0,y.jsx)("h1",{className:"woocommerce-payments-test-account-step__success_content_title",children:(0,i.__)("You're ready to test payments!","woocommerce")}),(0,y.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,y.jsx)("div",{className:"woocommerce-woopayments-modal__content__item__description",children:(0,y.jsx)("p",{children:(0,c.A)({mixedString:(0,i.__)("We've created a test account for you so that you can begin {{link}}testing payments on your store{{/link}}.","woocommerce"),components:{link:(0,y.jsx)(d.Link,{href:"https://woocommerce.com/document/woopayments/testing-and-troubleshooting/sandbox-mode/",target:"_blank",rel:"noreferrer",type:"external"}),break:(0,y.jsx)("br",{})}})})})}),(0,y.jsxs)("div",{className:"woocommerce-payments-test-account-step__success-whats-next",children:[(0,y.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,y.jsx)("h2",{children:(0,i.__)("What's next:","woocommerce")})}),(0,y.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex",children:[(0,y.jsx)("img",{src:_.GZ+"images/icons/store.svg",alt:"store icon"}),(0,y.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex__description",children:[(0,y.jsx)("h3",{children:(0,i.__)("Continue setting up your store","woocommerce")}),(0,y.jsx)("div",{children:(0,i.__)("Test payments and finish off any other tasks required to launch your store.","woocommerce")})]})]}),(0,y.jsx)(l.Button,{variant:"primary",onClick:()=>{(0,h.W7)("woopayments_onboarding_modal_click",{step:e?.id||"unknown",action:"continue_store_setup",source:b}),(0,m.navigateTo)({url:(0,m.getNewPath)({},"",{page:"wc-admin"})})},children:(0,i.__)("Continue store setup","woocommerce")}),(0,y.jsxs)("div",{className:"woocommerce-payments-test-account-step__success_content_or-divider",children:[(0,y.jsx)("hr",{}),(0,i.__)("OR","woocommerce"),(0,y.jsx)("hr",{})]}),(0,y.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex",children:[(0,y.jsx)("img",{src:_.GZ+"images/icons/dollar.svg",alt:"dollar icon"}),(0,y.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex__description",children:[(0,y.jsx)("h3",{children:(0,i.__)("Activate real payments","woocommerce")}),(0,y.jsx)("div",{children:(0,y.jsx)("p",{children:(0,c.A)({mixedString:(0,i.__)("Provide additional details about your business so you can begin accepting real payments. {{link}}Learn more{{/link}}","woocommerce"),components:{link:(0,y.jsx)(d.Link,{href:"https://woocommerce.com/document/woopayments/startup-guide/#sign-up-process",target:"_blank",rel:"noreferrer",type:"external"})}})})})]})]}),(0,y.jsx)(l.Button,{variant:"secondary",isBusy:H,disabled:H,onClick:()=>{(0,h.W7)("woopayments_onboarding_modal_click",{step:e?.id||"unknown",action:"activate_payments",source:b}),V(!0),(0,h.wJ)().then((()=>(V(!1),t(),n()))).catch((()=>{V(!1)}))},children:(0,i.__)("Activate payments","woocommerce")})]})]})})})]});const K="woocommerce_woopayments_test_account_already_exists"===D?[{label:(0,i.__)("Reset Account","woocommerce"),variant:"secondary",onClick:()=>{I(!0)}}]:[{label:(0,i.__)("Try Again","woocommerce"),variant:"primary",onClick:()=>{(0,h.W7)("woopayments_onboarding_modal_click",{step:e?.id||"unknown",action:"try_again_on_error",retries:P+1,source:b}),U(),E((e=>e+1))}},{label:(0,i.__)("Cancel","woocommerce"),variant:"secondary",className:"woocommerce-payments-test-account-step__error-cancel-button",onClick:()=>{(0,h.W7)("woopayments_onboarding_modal_click",{step:e?.id||"unknown",action:"cancel_on_error",retries:P,source:b}),o()}}];return(0,y.jsxs)("div",{className:"woocommerce-payments-test-account-step",children:[(0,y.jsx)(u.A,{onClose:o}),("error"===f||"blocked"===f)&&(0,y.jsx)(l.Notice,{status:"blocked"===f?"error":"warning",isDismissible:!1,actions:"blocked"!==f?K:[],className:"woocommerce-payments-test-account-step__error",children:(0,y.jsx)("p",{className:"woocommerce-payments-test-account-step__error-message",children:C||(0,i.__)("An error occurred while creating your test account. Please try again.","woocommerce")})}),("initializing"===f||"polling"===f)&&(0,y.jsx)(w,{progress:S,title:A,message:(G=F,1===G?(0,i.__)("The test account creation is taking a bit longer than expected, but don't worry — we're on it! Please bear with us for a few seconds more as we set everything up for your store.","woocommerce"):2===G?(0,i.__)("Thank you for your patience! Unfortunately, the test account creation is taking a bit longer than we anticipated. But don't worry — we won't give up! Feel free to close this modal and check back later. We appreciate your understanding!","woocommerce"):void 0)}),(0,y.jsx)(g.M,{isOpen:W,onClose:()=>{I(!1),v({show:!0,message:(0,i.__)("Your test account was successfully reset.","woocommerce")})},isEmbeddedResetFlow:!0})]});var G}},10432:(e,t,o)=>{o.d(t,{A:()=>d}),o(51609);var s=o(27723),n=o(56427),r=o(86087),a=o(99096),i=o(7175),c=o(1069),l=o(39793);const d=()=>{const{currentStep:e,closeModal:t,sessionEntryPoint:o}=(0,a.w)(),[d,m]=(0,r.useState)(!1);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.A,{onClose:t}),(0,l.jsx)("div",{className:"settings-payments-onboarding-modal__step--content",children:(0,l.jsxs)("div",{className:"settings-payments-onboarding-modal__step--content-jetpack",children:[(0,l.jsx)("h1",{className:"settings-payments-onboarding-modal__step--content-jetpack-title",children:(0,s.__)("Connect to WordPress.com","woocommerce")}),(0,l.jsx)("p",{className:"settings-payments-onboarding-modal__step--content-jetpack-description",children:(0,s.__)("You’ll be briefly redirected to connect your store to your WordPress.com account and unlock the full features and functionality of WooPayments","woocommerce")}),(0,l.jsx)(n.Button,{variant:"primary",className:"settings-payments-onboarding-modal__step--content-jetpack-button",isBusy:d,disabled:d,onClick:()=>{var t;(0,c.W7)("woopayments_onboarding_modal_click",{step:e?.id||"unknown",action:"connect_to_wpcom",source:o}),m(!0),window.location.href=null!==(t=e?.actions?.auth?.href)&&void 0!==t?t:""},children:(0,s.__)("Connect","woocommerce")})]})})]})}}}]);