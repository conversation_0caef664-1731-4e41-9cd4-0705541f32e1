"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[5057],{3172:(e,s,c)=>{c.r(s),c.d(s,{default:()=>o});var r=c(4656),t=c(910),a=c(5460),n=c(790);const o=({className:e})=>{const{cartFees:s,cartTotals:c}=(0,a.V)();if(!s.length)return null;const o=(0,t.getCurrencyFromPriceResponse)(c);return(0,n.jsx)(r.TotalsWrapper,{className:e,children:(0,n.jsx)(r.TotalsFees,{currency:o,cartFees:s})})}}}]);