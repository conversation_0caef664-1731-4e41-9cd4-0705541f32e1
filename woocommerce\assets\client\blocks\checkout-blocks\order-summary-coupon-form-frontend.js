"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[6382],{4594:(n,o,c)=>{c.r(o),c.d(o,{default:()=>l});var s=c(749),a=c(5954),e=c(5703),t=c(4656),p=c(790);const l=({className:n=""})=>{const o=(0,e.getSetting)("couponsEnabled",!0),{applyCoupon:c,isApplyingCoupon:l}=(0,a.k)("wc/checkout");return o?(0,p.jsx)(t.TotalsWrapper,{className:n,children:(0,p.jsx)(s._i,{onSubmit:c,isLoading:l,instanceId:"coupon"})}):null}}}]);