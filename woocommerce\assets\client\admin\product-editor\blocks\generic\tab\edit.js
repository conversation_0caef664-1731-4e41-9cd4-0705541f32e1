"use strict";var __importDefault=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.TabBlockEdit=TabBlockEdit;const i18n_1=require("@wordpress/i18n"),block_editor_1=require("@wordpress/block-editor"),clsx_1=__importDefault(require("clsx")),element_1=require("@wordpress/element"),block_templates_1=require("@woocommerce/block-templates"),components_1=require("@woocommerce/components"),tab_button_1=require("./tab-button");function TabBlockEdit({setAttributes:e,attributes:t,context:r}){const o=(0,block_templates_1.useWooBlockProps)(t),{id:c,title:n,_templateBlockOrder:l,isSelected:s}=t,a=(0,clsx_1.default)("wp-block-woocommerce-product-tab__content",{"is-selected":s}),[i,_]=(0,element_1.useState)(!1);return(0,element_1.useEffect)((()=>{if(!r.selectedTab)return;const t=r.selectedTab===c;if(e({isSelected:t}),t)return void _(!0);const o=setTimeout(_,300,!0);return()=>clearTimeout(o)}),[r.selectedTab,c,e]),(0,element_1.createElement)("div",{...o},(0,element_1.createElement)(tab_button_1.TabButton,{id:c,selected:s,order:l},n),(0,element_1.createElement)("div",{id:`woocommerce-product-tab__${c}-content`,"aria-labelledby":`woocommerce-product-tab__${c}`,role:"tabpanel",className:a},(0,element_1.createElement)(components_1.__experimentalErrorBoundary,{errorMessage:(0,i18n_1.__)("An unexpected error occurred in this tab. Make sure any unsaved changes are saved and then try reloading the page to see if the error recurs.","woocommerce"),onError:(e,t)=>{console.error(`Error caught in tab '${c}'`,e,t)}},i&&(0,element_1.createElement)(block_editor_1.InnerBlocks,{templateLock:"contentOnly"}))))}