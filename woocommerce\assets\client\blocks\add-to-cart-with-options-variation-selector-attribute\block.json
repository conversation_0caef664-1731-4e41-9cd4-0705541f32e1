{"name": "woocommerce/add-to-cart-with-options-variation-selector-attribute", "title": "Variation Selector: <PERSON><PERSON><PERSON> (Beta)", "description": "A template for attribute name and options that will be applied to all variable products with attributes.", "category": "woocommerce-product-elements", "keywords": ["WooCommerce"], "usesContext": ["postId"], "ancestor": ["woocommerce/add-to-cart-with-options-variation-selector"], "textdomain": "woocommerce", "apiVersion": 3, "$schema": "https://schemas.wp.org/trunk/block.json", "supports": {"inserter": false, "interactivity": true}}